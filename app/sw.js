/**
 * Service Worker for Catalan Learning PWA
 * Provides offline functionality and caching
 */

const CACHE_NAME = 'catalan-pwa-v1.0.0';
const DATA_CACHE_NAME = 'catalan-data-v1.0.0';

// Files to cache for offline functionality
const FILES_TO_CACHE = [
  '/',
  '/index.html',
  '/css/main.css',
  '/css/components.css',
  '/css/responsive.css',
  '/js/db.js',
  '/js/importer.js',
  '/js/srs.js',
  '/js/ui_components.js',
  '/js/ui_study.js',
  '/js/ui_editor.js',
  '/js/ui_analytics.js',
  '/js/main.js',
  '/manifest.webmanifest',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// Data files to cache
const DATA_FILES = [
  '/data/vocabulary_decks.jsonl',
  '/data/sentence_decks.jsonl',
  '/data/grammar_decks.jsonl',
  '/data/conjugation_decks.jsonl',
  '/data/topic_decks.jsonl',
  '/data/deck_manifest.json',
  '/data/audio_manifest.json',
  '/data/import_manifest.json'
];

// Install event - cache core files
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...');
  
  event.waitUntil(
    Promise.all([
      // Cache core app files
      caches.open(CACHE_NAME).then((cache) => {
        console.log('[SW] Caching app shell files');
        return cache.addAll(FILES_TO_CACHE);
      }),
      // Cache data files
      caches.open(DATA_CACHE_NAME).then((cache) => {
        console.log('[SW] Caching data files');
        return cache.addAll(DATA_FILES.map(file => {
          return fetch(file).then(response => {
            if (response.ok) {
              return cache.put(file, response);
            }
            console.warn(`[SW] Failed to cache ${file}: ${response.status}`);
          }).catch(error => {
            console.warn(`[SW] Failed to fetch ${file}:`, error);
          });
        }));
      })
    ]).then(() => {
      console.log('[SW] Installation complete');
      self.skipWaiting();
    })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== DATA_CACHE_NAME) {
            console.log('[SW] Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('[SW] Activation complete');
      self.clients.claim();
    })
  );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Handle data files
  if (url.pathname.startsWith('/data/')) {
    event.respondWith(
      caches.open(DATA_CACHE_NAME).then((cache) => {
        return cache.match(request).then((response) => {
          if (response) {
            console.log('[SW] Serving data from cache:', url.pathname);
            return response;
          }
          
          // Try network, then cache the response
          return fetch(request).then((networkResponse) => {
            if (networkResponse.ok) {
              cache.put(request, networkResponse.clone());
            }
            return networkResponse;
          }).catch(() => {
            console.warn('[SW] Data file not available offline:', url.pathname);
            return new Response('Data not available offline', { status: 503 });
          });
        });
      })
    );
    return;
  }
  
  // Handle audio files
  if (url.pathname.startsWith('/media/audio/')) {
    event.respondWith(
      caches.match(request).then((response) => {
        if (response) {
          return response;
        }
        
        return fetch(request).then((networkResponse) => {
          if (networkResponse.ok) {
            const responseClone = networkResponse.clone();
            caches.open(CACHE_NAME).then((cache) => {
              cache.put(request, responseClone);
            });
          }
          return networkResponse;
        }).catch(() => {
          console.warn('[SW] Audio file not available offline:', url.pathname);
          return new Response('Audio not available offline', { status: 503 });
        });
      })
    );
    return;
  }
  
  // Handle app shell files
  event.respondWith(
    caches.match(request).then((response) => {
      if (response) {
        console.log('[SW] Serving from cache:', url.pathname);
        return response;
      }
      
      // Try network for non-cached files
      return fetch(request).then((networkResponse) => {
        // Cache successful responses for future use
        if (networkResponse.ok && request.method === 'GET') {
          const responseClone = networkResponse.clone();
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(request, responseClone);
          });
        }
        return networkResponse;
      }).catch(() => {
        // Fallback for navigation requests
        if (request.mode === 'navigate') {
          return caches.match('/index.html');
        }
        
        console.warn('[SW] Resource not available offline:', url.pathname);
        return new Response('Resource not available offline', { status: 503 });
      });
    })
  );
});

// Background sync for data updates
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);
  
  if (event.tag === 'update-progress') {
    event.waitUntil(syncProgress());
  }
  
  if (event.tag === 'backup-data') {
    event.waitUntil(backupUserData());
  }
});

// Push notifications (for future use)
self.addEventListener('push', (event) => {
  console.log('[SW] Push notification received');
  
  const options = {
    body: 'Time for your daily Catalan practice!',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: 'daily-reminder',
    requireInteraction: true,
    actions: [
      {
        action: 'study',
        title: 'Start Studying',
        icon: '/icons/action-study.png'
      },
      {
        action: 'dismiss',
        title: 'Later',
        icon: '/icons/action-dismiss.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('Aprèn Català', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event.action);
  
  event.notification.close();
  
  if (event.action === 'study') {
    event.waitUntil(
      clients.openWindow('/?action=study')
    );
  }
});

// Helper functions
async function syncProgress() {
  try {
    // Sync user progress with local storage
    console.log('[SW] Syncing progress data...');
    // Implementation would sync with IndexedDB
  } catch (error) {
    console.error('[SW] Failed to sync progress:', error);
  }
}

async function backupUserData() {
  try {
    // Create backup of user data
    console.log('[SW] Creating data backup...');
    // Implementation would backup user progress and settings
  } catch (error) {
    console.error('[SW] Failed to backup data:', error);
  }
}

// Message handling from main thread
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_AUDIO') {
    const { audioFiles } = event.data;
    cacheAudioFiles(audioFiles);
  }
});

async function cacheAudioFiles(audioFiles) {
  try {
    const cache = await caches.open(CACHE_NAME);
    
    for (const audioFile of audioFiles) {
      try {
        const response = await fetch(audioFile.url);
        if (response.ok) {
          await cache.put(audioFile.url, response);
          console.log('[SW] Cached audio file:', audioFile.url);
        }
      } catch (error) {
        console.warn('[SW] Failed to cache audio file:', audioFile.url, error);
      }
    }
  } catch (error) {
    console.error('[SW] Failed to cache audio files:', error);
  }
}
