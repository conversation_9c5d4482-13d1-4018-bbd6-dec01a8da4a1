<!DOCTYPE html>
<html lang="ca">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aprèn Català - Catalan Learning PWA</title>
    
    <!-- PWA Meta Tags -->
    <meta name="description" content="Complete offline Catalan learning app with SRS, multiple review modes, and comprehensive content">
    <meta name="theme-color" content="#d32f2f">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Aprèn Català">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/favicon-16x16.png">
    <link rel="apple-touch-icon" href="icons/apple-touch-icon.png">
    <link rel="manifest" href="manifest.webmanifest">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="js/db.js" as="script">
    <link rel="preload" href="js/main.js" as="script">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="logo">
                <h1>Aprèn Català</h1>
                <p>Carregant...</p>
            </div>
            <div class="loading-bar">
                <div class="loading-progress" id="loading-progress"></div>
            </div>
            <div class="loading-text" id="loading-text">Inicialitzant aplicació...</div>
        </div>
    </div>

    <!-- Import Wizard (First Run) -->
    <div id="import-wizard" class="modal hidden">
        <div class="modal-content">
            <h2>Benvingut a Aprèn Català!</h2>
            <p>Importem el contingut d'aprenentatge. Això pot trigar uns minuts...</p>
            
            <div class="import-progress">
                <div class="progress-item" id="progress-decks">
                    <span class="progress-label">Baralles de vocabulari</span>
                    <div class="progress-bar">
                        <div class="progress-fill" data-progress="decks"></div>
                    </div>
                    <span class="progress-text" data-text="decks">0%</span>
                </div>
                
                <div class="progress-item" id="progress-sentences">
                    <span class="progress-label">Frases d'exemple</span>
                    <div class="progress-bar">
                        <div class="progress-fill" data-progress="sentences"></div>
                    </div>
                    <span class="progress-text" data-text="sentences">0%</span>
                </div>
                
                <div class="progress-item" id="progress-grammar">
                    <span class="progress-label">Gramàtica i conjugacions</span>
                    <div class="progress-bar">
                        <div class="progress-fill" data-progress="grammar"></div>
                    </div>
                    <span class="progress-text" data-text="grammar">0%</span>
                </div>
            </div>
            
            <div class="import-actions">
                <button id="pause-import" class="btn btn-secondary">Pausar</button>
                <button id="resume-import" class="btn btn-primary hidden">Continuar</button>
            </div>
            
            <div class="import-status" id="import-status">
                Preparant importació...
            </div>
        </div>
    </div>

    <!-- Main App -->
    <div id="app" class="app hidden">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">Aprèn Català</h1>
                <div class="header-actions">
                    <button id="search-btn" class="btn btn-icon" title="Cercar" onclick="window.app && window.app.showSearchModal()">
                        <span class="icon">🔍</span>
                    </button>
                    <button id="settings-btn" class="btn btn-icon" title="Configuració" onclick="window.app && window.app.showSettingsModal()">
                        <span class="icon">⚙️</span>
                    </button>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="main-nav">
                <button class="nav-item active" data-view="dashboard" onclick="window.app && window.app.navigateToView('dashboard')">
                    <span class="icon">🏠</span>
                    <span class="label">Inici</span>
                </button>
                <button class="nav-item" data-view="study" onclick="window.app && window.app.navigateToView('study')">
                    <span class="icon">📚</span>
                    <span class="label">Estudiar</span>
                </button>
                <button class="nav-item" data-view="decks" onclick="window.app && window.app.navigateToView('decks')">
                    <span class="icon">🃏</span>
                    <span class="label">Baralles</span>
                </button>
                <button class="nav-item" data-view="editor" onclick="window.app && window.app.navigateToView('editor')">
                    <span class="icon">✏️</span>
                    <span class="label">Editor</span>
                </button>
                <button class="nav-item" data-view="analytics" onclick="window.app && window.app.navigateToView('analytics')">
                    <span class="icon">📊</span>
                    <span class="label">Estadístiques</span>
                </button>
            </nav>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Dashboard View -->
            <div id="dashboard-view" class="view active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <h3>Sessió d'avui</h3>
                        <div class="stats">
                            <div class="stat">
                                <span class="stat-value" id="today-reviews">0</span>
                                <span class="stat-label">Repassos</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value" id="today-new">0</span>
                                <span class="stat-label">Noves</span>
                            </div>
                        </div>
                        <button id="start-study" class="btn btn-primary btn-large" onclick="window.app && window.app.startStudySession()">
                            Començar a estudiar
                        </button>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>Progrés</h3>
                        <div class="progress-overview">
                            <div class="level-progress">
                                <span class="level">A2</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 65%"></div>
                                </div>
                                <span class="percentage">65%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>Baralles recents</h3>
                        <div id="recent-decks" class="deck-list">
                            <!-- Populated by JS -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Study View -->
            <div id="study-view" class="view">
                <div class="study-container">
                    <div class="study-header">
                        <div class="study-progress">
                            <span id="card-counter">1 / 20</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="study-progress"></div>
                            </div>
                        </div>
                        <div class="study-actions">
                            <button id="study-settings" class="btn btn-icon">⚙️</button>
                            <button id="exit-study" class="btn btn-icon">❌</button>
                        </div>
                    </div>
                    
                    <div class="card-container" id="card-container">
                        <!-- Study cards populated by JS -->
                    </div>
                    
                    <div class="study-controls" id="study-controls">
                        <!-- Study controls populated by JS -->
                    </div>
                </div>
            </div>

            <!-- Other views will be populated by JS -->
            <div id="decks-view" class="view"></div>
            <div id="editor-view" class="view"></div>
            <div id="analytics-view" class="view"></div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="js/db.js"></script>
    <script src="js/importer.js"></script>
    <script src="js/srs.js"></script>
    <script src="js/ui_components.js"></script>
    <script src="js/ui_study.js"></script>
    <script src="js/ui_editor.js"></script>
    <script src="js/ui_analytics.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
