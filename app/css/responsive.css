/**
 * Responsive styles for Catalan Learning PWA
 */

/* Analytics styles */
.analytics-container {
  max-width: 1200px;
  margin: 0 auto;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.analytics-card {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-1);
}

.analytics-card h3 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: var(--font-size-lg);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-sm);
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--primary-color);
  display: block;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* Chart styles */
.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.simple-chart {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 150px;
  width: 100%;
  gap: 2px;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.bar-fill {
  background: var(--primary-color);
  width: 100%;
  min-height: 2px;
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
}

.bar-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.chart-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.chart-label {
  min-width: 30px;
  font-weight: 500;
}

.chart-bar-horizontal {
  flex: 1;
  height: 20px;
  background: var(--surface-variant);
  border-radius: 10px;
  overflow: hidden;
}

.chart-value {
  min-width: 40px;
  text-align: right;
  font-weight: 500;
}

/* Performance styles */
.performance-item {
  margin-bottom: var(--spacing-md);
}

.performance-label {
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
}

.performance-bar {
  height: 8px;
  background: var(--surface-variant);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: var(--spacing-xs);
}

.performance-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.performance-value {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Leech cards */
.leech-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-sm);
}

.leech-content {
  flex: 1;
}

.leech-translation {
  color: var(--text-secondary);
  margin-left: var(--spacing-sm);
}

.leech-stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.leech-lapses {
  font-size: var(--font-size-sm);
  color: var(--error-color);
  font-weight: 500;
}

/* Time stats */
.time-stat {
  text-align: center;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-sm);
}

.time-value {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--primary-color);
}

.time-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* Streak display */
.streak-display {
  text-align: center;
  padding: var(--spacing-lg);
}

.streak-emoji {
  font-size: 3rem;
  margin-bottom: var(--spacing-sm);
}

.streak-number {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--primary-color);
}

.streak-label {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.streak-message {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--success-color);
}

/* Editor styles */
.editor-container {
  max-width: 1200px;
  margin: 0 auto;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.editor-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.editor-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: var(--spacing-xl);
  min-height: 600px;
}

.editor-sidebar {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-1);
}

.search-box {
  margin-bottom: var(--spacing-md);
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.cards-list {
  max-height: 500px;
  overflow-y: auto;
}

.card-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: var(--transition);
}

.card-list-item:hover {
  background: var(--surface-variant);
  border-color: var(--primary-color);
}

.card-list-item.selected {
  background: rgba(211, 47, 47, 0.1);
  border-color: var(--primary-color);
}

.card-list-content {
  flex: 1;
}

.card-list-main {
  margin-bottom: var(--spacing-xs);
}

.card-list-translation {
  color: var(--text-secondary);
  margin-left: var(--spacing-sm);
}

.card-list-meta {
  display: flex;
  gap: var(--spacing-xs);
}

.card-list-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.editor-main {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-1);
}

.editor-welcome {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.card-form {
  max-width: 600px;
}

.card-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

/* Pie chart styles */
.pie-slice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  border-left: 4px solid var(--primary-color);
  margin-bottom: var(--spacing-sm);
  background: var(--surface-variant);
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.slice-label {
  font-weight: 500;
}

.slice-value {
  color: var(--text-secondary);
}

/* Session complete styles */
.session-complete {
  text-align: center;
  padding: var(--spacing-xl);
}

.session-complete h2 {
  margin-bottom: var(--spacing-xl);
  color: var(--success-color);
}

.session-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.session-breakdown {
  background: var(--surface-variant);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.breakdown-label {
  font-weight: 500;
}

.breakdown-value {
  color: var(--text-secondary);
}

/* Responsive breakpoints */
@media (max-width: 1024px) {
  .analytics-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .editor-content {
    grid-template-columns: 250px 1fr;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .analytics-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .editor-content {
    grid-template-columns: 1fr;
  }
  
  .editor-sidebar {
    order: 2;
  }
  
  .editor-main {
    order: 1;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .session-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .analytics-card,
  .editor-sidebar,
  .editor-main {
    padding: var(--spacing-md);
  }
  
  .chart-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .chart-label {
    min-width: auto;
  }
  
  .chart-value {
    text-align: left;
  }
  
  .leech-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .leech-stats {
    justify-content: space-between;
  }
  
  .card-list-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .card-list-actions {
    justify-content: flex-end;
  }
}

/* Deck styles */
.decks-container {
  max-width: 1200px;
  margin: 0 auto;
}

.decks-container h2 {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.decks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.deck-card {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-1);
  cursor: pointer;
  transition: var(--transition);
}

.deck-card:hover {
  box-shadow: var(--shadow-2);
  transform: translateY(-2px);
}

.deck-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.deck-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.deck-type {
  background: var(--primary-color);
  color: var(--text-on-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
}

.deck-stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.deck-stat {
  text-align: center;
}

.deck-description {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
  margin: 0;
}

/* Print styles */
@media print {
  .analytics-container {
    max-width: none;
  }

  .analytics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .analytics-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid var(--border-color);
  }

  .btn,
  .editor-actions,
  .card-list-actions {
    display: none;
  }
}
