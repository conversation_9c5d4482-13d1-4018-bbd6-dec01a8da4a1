/**
 * Component styles for Catalan Learning PWA
 */

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: var(--font-size-md);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  min-height: 40px;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--text-on-primary);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-dark);
  box-shadow: var(--shadow-2);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-on-primary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--secondary-dark);
  box-shadow: var(--shadow-2);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--text-on-primary);
}

.btn-ghost {
  background: transparent;
  color: var(--text-primary);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--surface-variant);
}

.btn-icon {
  padding: var(--spacing-sm);
  min-width: 40px;
  border-radius: 50%;
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-lg);
  min-height: 48px;
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-height: 32px;
}

/* Cards */
.card {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-1);
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-2);
}

.card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-variant);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  margin: 0;
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0 0;
}

.card-content {
  padding: var(--spacing-md);
}

.card-actions {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
}

.modal-content {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-3);
}

.modal h2 {
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

/* Progress components */
.progress-item {
  margin-bottom: var(--spacing-lg);
}

.progress-label {
  display: block;
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-left: var(--spacing-sm);
}

/* Import wizard specific */
.import-progress {
  margin: var(--spacing-xl) 0;
}

.import-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin: var(--spacing-xl) 0;
}

.import-status {
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  padding: var(--spacing-md);
  background: var(--surface-variant);
  border-radius: var(--border-radius);
}

/* Study components */
.study-container {
  max-width: 800px;
  margin: 0 auto;
}

.study-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-md);
  background: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-1);
}

.study-progress {
  flex: 1;
  margin-right: var(--spacing-lg);
}

.study-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.card-container {
  margin-bottom: var(--spacing-xl);
}

.study-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xl);
  text-align: center;
  box-shadow: var(--shadow-2);
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: var(--transition);
}

.study-card.flipped {
  background: var(--surface-variant);
}

.card-front, .card-back {
  display: none;
}

.card-front.active, .card-back.active {
  display: block;
}

.card-word {
  font-size: var(--font-size-xxl);
  font-weight: 300;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.card-ipa {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-family: 'Lucida Sans Unicode', 'Arial Unicode MS', sans-serif;
}

.card-translation {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.card-examples {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  font-style: italic;
}

.study-controls {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.difficulty-btn {
  flex: 1;
  min-width: 120px;
  max-width: 200px;
}

.difficulty-again {
  background: var(--error-color);
  color: var(--text-on-primary);
}

.difficulty-hard {
  background: var(--warning-color);
  color: var(--text-on-primary);
}

.difficulty-good {
  background: var(--success-color);
  color: var(--text-on-primary);
}

.difficulty-easy {
  background: var(--info-color);
  color: var(--text-on-primary);
}

/* Deck list */
.deck-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.deck-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--surface-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: var(--transition);
}

.deck-item:hover {
  background: var(--surface-variant);
  border-color: var(--primary-color);
}

.deck-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
}

.deck-info p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.deck-stats {
  text-align: right;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Forms */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: var(--font-size-md);
  transition: var(--transition);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.form-select {
  background: var(--surface-color);
  cursor: pointer;
}

/* Tags */
.tag {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-color);
  color: var(--text-on-primary);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;
  margin: 0 var(--spacing-xs) var(--spacing-xs) 0;
}

.tag-secondary {
  background: var(--text-secondary);
}

.tag-outline {
  background: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

/* Alerts */
.alert {
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  border-left: 4px solid;
}

.alert-success {
  background: rgba(56, 142, 60, 0.1);
  border-color: var(--success-color);
  color: var(--success-color);
}

.alert-warning {
  background: rgba(245, 124, 0, 0.1);
  border-color: var(--warning-color);
  color: var(--warning-color);
}

.alert-error {
  background: rgba(211, 47, 47, 0.1);
  border-color: var(--error-color);
  color: var(--error-color);
}

.alert-info {
  background: rgba(25, 118, 210, 0.1);
  border-color: var(--info-color);
  color: var(--info-color);
}

/* Loading states */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.skeleton {
  background: linear-gradient(90deg, var(--surface-variant) 25%, var(--border-color) 50%, var(--surface-variant) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
