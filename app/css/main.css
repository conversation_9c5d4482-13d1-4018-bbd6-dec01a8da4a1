/**
 * Main styles for Catalan Learning PWA
 */

/* CSS Variables */
:root {
  --primary-color: #d32f2f;
  --primary-light: #ff6659;
  --primary-dark: #9a0007;
  --secondary-color: #1976d2;
  --secondary-light: #63a4ff;
  --secondary-dark: #004ba0;
  
  --success-color: #388e3c;
  --warning-color: #f57c00;
  --error-color: #d32f2f;
  --info-color: #1976d2;
  
  --background-color: #fafafa;
  --surface-color: #ffffff;
  --surface-variant: #f5f5f5;
  
  --text-primary: #212121;
  --text-secondary: #757575;
  --text-disabled: #bdbdbd;
  --text-on-primary: #ffffff;
  
  --border-color: #e0e0e0;
  --divider-color: #e0e0e0;
  
  --shadow-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --shadow-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
  
  --border-radius: 8px;
  --border-radius-small: 4px;
  --border-radius-large: 16px;
  
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  --font-size-xxl: 32px;
  
  --transition: all 0.2s ease-in-out;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-color);
  color: var(--text-primary);
  overflow-x: hidden;
}

/* Loading screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  color: var(--text-on-primary);
}

.loading-content {
  text-align: center;
  max-width: 400px;
  padding: var(--spacing-xl);
}

.loading-content .logo h1 {
  font-size: var(--font-size-xxl);
  margin-bottom: var(--spacing-sm);
  font-weight: 300;
}

.loading-content .logo p {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin-bottom: var(--spacing-xl);
}

.loading-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.loading-progress {
  height: 100%;
  background: var(--text-on-primary);
  border-radius: 2px;
  transition: width 0.3s ease;
  width: 0%;
}

.loading-text {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

/* App layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  max-width: 1200px;
  margin: 0 auto;
}

.app-title {
  font-size: var(--font-size-xl);
  font-weight: 500;
  color: var(--primary-color);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Navigation */
.main-nav {
  display: flex;
  background: var(--surface-color);
  border-top: 1px solid var(--border-color);
  overflow-x: auto;
}

.nav-item {
  flex: 1;
  min-width: 80px;
  padding: var(--spacing-sm);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  text-decoration: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  transition: var(--transition);
  font-size: var(--font-size-xs);
}

.nav-item:hover {
  background: var(--surface-variant);
  color: var(--text-primary);
}

.nav-item.active {
  color: var(--primary-color);
  background: rgba(211, 47, 47, 0.1);
}

.nav-item .icon {
  font-size: var(--font-size-lg);
}

.nav-item .label {
  font-weight: 500;
}

/* Main content */
.app-main {
  flex: 1;
  padding: var(--spacing-md);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Views */
.view {
  display: none;
}

.view.active {
  display: block;
}

/* Dashboard */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.dashboard-card {
  background: var(--surface-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-1);
  transition: var(--transition);
}

.dashboard-card:hover {
  box-shadow: var(--shadow-2);
}

.dashboard-card h3 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--primary-color);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Progress indicators */
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--surface-variant);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.level-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.level {
  font-weight: 600;
  color: var(--primary-color);
  min-width: 30px;
}

.percentage {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  min-width: 40px;
  text-align: right;
}

/* Utility classes */
.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* Responsive design */
@media (max-width: 768px) {
  .app-main {
    padding: var(--spacing-sm);
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .header-content {
    padding: var(--spacing-sm);
  }
  
  .app-title {
    font-size: var(--font-size-lg);
  }
  
  .stats {
    justify-content: space-around;
  }
}

@media (max-width: 480px) {
  .nav-item .label {
    display: none;
  }
  
  .nav-item {
    min-width: 60px;
  }
}
