/**
 * Main Application Controller
 * Handles app initialization, routing, and core functionality
 */

class CatalanApp {
  constructor() {
    this.currentView = 'dashboard';
    this.isInitialized = false;
    this.serviceWorkerRegistration = null;
    this.currentSession = null;
  }

  async init() {
    try {
      console.log('🚀 Initializing Catalan Learning PWA...');
      
      // Show loading screen
      this.showLoadingScreen();
      this.updateLoadingProgress(10, 'Initializing database...');
      
      // Initialize database
      await db.init();
      this.updateLoadingProgress(30, 'Checking import status...');
      
      // Check if data needs to be imported
      const isImported = await importer.checkImportStatus();
      
      if (!isImported) {
        this.updateLoadingProgress(50, 'Preparing data import...');
        await this.showImportWizard();
      } else {
        this.updateLoadingProgress(70, 'Loading application...');
        await this.initializeApp();
      }
      
    } catch (error) {
      console.error('❌ Failed to initialize app:', error);
      this.showError('Failed to initialize application', error.message);
    }
  }

  async initializeApp() {
    try {
      // Register service worker
      this.updateLoadingProgress(80, 'Registering service worker...');
      await this.registerServiceWorker();
      
      // Setup event listeners
      this.updateLoadingProgress(90, 'Setting up interface...');
      this.setupEventListeners();
      
      // Load initial data
      await this.loadDashboardData();
      
      // Hide loading screen and show app
      this.updateLoadingProgress(100, 'Ready!');
      setTimeout(() => {
        this.hideLoadingScreen();
        this.showApp();
        this.isInitialized = true;
        console.log('✅ App initialized successfully');
      }, 500);
      
    } catch (error) {
      console.error('❌ Failed to initialize app:', error);
      this.showError('Initialization failed', error.message);
    }
  }

  async registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        this.serviceWorkerRegistration = await navigator.serviceWorker.register('/sw.js');
        console.log('✅ Service Worker registered successfully');
        
        // Listen for updates
        this.serviceWorkerRegistration.addEventListener('updatefound', () => {
          console.log('🔄 Service Worker update found');
        });
        
      } catch (error) {
        console.warn('⚠️ Service Worker registration failed:', error);
      }
    }
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const view = item.dataset.view;
        if (view) {
          this.navigateToView(view);
        }
      });
    });

    // Study button
    const startStudyBtn = document.getElementById('start-study');
    if (startStudyBtn) {
      startStudyBtn.addEventListener('click', () => {
        this.startStudySession();
      });
    }

    // Search button
    const searchBtn = document.getElementById('search-btn');
    if (searchBtn) {
      searchBtn.addEventListener('click', () => {
        this.showSearchModal();
      });
    }

    // Settings button
    const settingsBtn = document.getElementById('settings-btn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.showSettingsModal();
      });
    }

    // Import wizard events
    this.setupImportWizardEvents();

    // Custom events
    window.addEventListener('importComplete', () => {
      this.onImportComplete();
    });

    // Handle URL parameters
    this.handleUrlParams();
  }

  setupImportWizardEvents() {
    const pauseBtn = document.getElementById('pause-import');
    const resumeBtn = document.getElementById('resume-import');

    if (pauseBtn) {
      pauseBtn.addEventListener('click', () => {
        importer.pauseImport();
        pauseBtn.classList.add('hidden');
        resumeBtn.classList.remove('hidden');
      });
    }

    if (resumeBtn) {
      resumeBtn.addEventListener('click', () => {
        importer.resumeImport();
        resumeBtn.classList.add('hidden');
        pauseBtn.classList.remove('hidden');
      });
    }
  }

  // Navigation
  navigateToView(viewName) {
    // Hide current view
    document.querySelectorAll('.view').forEach(view => {
      view.classList.remove('active');
    });

    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });

    // Show new view
    const newView = document.getElementById(`${viewName}-view`);
    const navItem = document.querySelector(`[data-view="${viewName}"]`);

    if (newView && navItem) {
      newView.classList.add('active');
      navItem.classList.add('active');
      this.currentView = viewName;

      // Load view-specific data
      this.loadViewData(viewName);
    }
  }

  async loadViewData(viewName) {
    switch (viewName) {
      case 'dashboard':
        await this.loadDashboardData();
        break;
      case 'study':
        await this.loadStudyView();
        break;
      case 'decks':
        await this.loadDecksView();
        break;
      case 'editor':
        await this.loadEditorView();
        break;
      case 'analytics':
        await this.loadAnalyticsView();
        break;
    }
  }

  // Dashboard
  async loadDashboardData() {
    try {
      const stats = await db.getStatistics();
      
      // Update today's stats
      document.getElementById('today-reviews').textContent = stats.today_reviews || 0;
      document.getElementById('today-new').textContent = stats.new_cards || 0;
      
      // Update recent decks
      await this.loadRecentDecks();
      
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  }

  async loadRecentDecks() {
    const recentDecksContainer = document.getElementById('recent-decks');
    if (!recentDecksContainer) return;

    try {
      // Get deck manifest
      const response = await fetch('/data/deck_manifest.json');
      const manifest = await response.json();
      
      recentDecksContainer.innerHTML = '';
      
      manifest.decks.slice(0, 3).forEach(deck => {
        const deckElement = document.createElement('div');
        deckElement.className = 'deck-item';
        deckElement.innerHTML = `
          <div class="deck-info">
            <h4>${deck.name}</h4>
            <p>${deck.card_count} cards</p>
          </div>
          <div class="deck-stats">
            <span class="tag">${deck.type}</span>
          </div>
        `;
        
        deckElement.addEventListener('click', () => {
          this.startDeckStudy(deck.id);
        });
        
        recentDecksContainer.appendChild(deckElement);
      });
      
    } catch (error) {
      console.error('Failed to load recent decks:', error);
      recentDecksContainer.innerHTML = '<p class="text-secondary">No decks available</p>';
    }
  }

  // Study session
  async startStudySession(options = {}) {
    try {
      console.log('🎯 Starting study session...');
      
      // Generate study session
      this.currentSession = await srs.generateStudySession(options);
      
      if (this.currentSession.cards.length === 0) {
        this.showMessage('No cards available for study', 'info');
        return;
      }
      
      // Navigate to study view
      this.navigateToView('study');
      
      // Initialize study UI
      this.initializeStudySession();
      
    } catch (error) {
      console.error('Failed to start study session:', error);
      this.showError('Failed to start study session', error.message);
    }
  }

  async startDeckStudy(deckId) {
    const options = {
      cardTypes: [deckId],
      maxCards: 20
    };
    await this.startStudySession(options);
  }

  initializeStudySession() {
    // This will be implemented in ui_study.js
    if (window.studyUI) {
      window.studyUI.initSession(this.currentSession);
    }
  }

  // Import wizard
  async showImportWizard() {
    const wizard = document.getElementById('import-wizard');
    const loadingScreen = document.getElementById('loading-screen');
    
    if (wizard && loadingScreen) {
      loadingScreen.classList.add('hidden');
      wizard.classList.remove('hidden');
      
      // Start import process
      setTimeout(() => {
        importer.startImport();
      }, 1000);
    }
  }

  async onImportComplete() {
    console.log('✅ Import completed, initializing app...');
    await this.initializeApp();
  }

  // UI helpers
  showLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      loadingScreen.classList.remove('hidden');
    }
  }

  hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      loadingScreen.classList.add('hidden');
    }
  }

  showApp() {
    const app = document.getElementById('app');
    if (app) {
      app.classList.remove('hidden');
    }
  }

  updateLoadingProgress(percentage, text) {
    const progressBar = document.getElementById('loading-progress');
    const progressText = document.getElementById('loading-text');
    
    if (progressBar) {
      progressBar.style.width = `${percentage}%`;
    }
    
    if (progressText) {
      progressText.textContent = text;
    }
  }

  showError(title, message) {
    console.error(`${title}: ${message}`);
    
    // Create error modal
    const errorModal = document.createElement('div');
    errorModal.className = 'modal';
    errorModal.innerHTML = `
      <div class="modal-content">
        <h2>❌ ${title}</h2>
        <p>${message}</p>
        <div class="modal-actions">
          <button class="btn btn-primary" onclick="this.closest('.modal').remove()">
            OK
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(errorModal);
  }

  showMessage(message, type = 'info') {
    const icons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    
    const messageModal = document.createElement('div');
    messageModal.className = 'modal';
    messageModal.innerHTML = `
      <div class="modal-content">
        <h2>${icons[type]} ${message}</h2>
        <div class="modal-actions">
          <button class="btn btn-primary" onclick="this.closest('.modal').remove()">
            OK
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(messageModal);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (messageModal.parentNode) {
        messageModal.remove();
      }
    }, 3000);
  }

  showSearchModal() {
    if (window.uiComponents) {
      uiComponents.createSearchModal();
      uiComponents.showModal('search-modal');
    }
  }

  showSettingsModal() {
    if (window.uiComponents) {
      uiComponents.createSettingsModal();
      uiComponents.showModal('settings-modal');
    }
  }

  handleUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');
    
    if (action === 'study') {
      // Start study session when app is ready
      setTimeout(() => {
        if (this.isInitialized) {
          this.startStudySession();
        }
      }, 1000);
    }
  }

  // View loading methods
  async loadStudyView() {
    // Implemented in ui_study.js
    if (this.currentSession) {
      studyUI.initSession(this.currentSession);
    }
  }

  async loadDecksView() {
    const decksView = document.getElementById('decks-view');
    if (!decksView) return;

    try {
      const response = await fetch('/data/deck_manifest.json');
      const manifest = await response.json();

      decksView.innerHTML = `
        <div class="decks-container">
          <h2>🃏 Baralles disponibles</h2>
          <div class="decks-grid">
            ${manifest.decks.map(deck => `
              <div class="deck-card" onclick="app.startDeckStudy('${deck.id}')">
                <div class="deck-header">
                  <h3>${deck.name}</h3>
                  <span class="deck-type">${deck.type}</span>
                </div>
                <div class="deck-stats">
                  <div class="deck-stat">
                    <span class="stat-value">${deck.card_count}</span>
                    <span class="stat-label">Cartes</span>
                  </div>
                  <div class="deck-stat">
                    <span class="stat-value">${deck.cefr_levels.join(', ')}</span>
                    <span class="stat-label">Nivells</span>
                  </div>
                </div>
                <p class="deck-description">${deck.description}</p>
              </div>
            `).join('')}
          </div>
        </div>
      `;
    } catch (error) {
      console.error('Failed to load decks:', error);
      decksView.innerHTML = '<p class="text-error">Error al carregar les baralles</p>';
    }
  }

  async loadEditorView() {
    if (window.editorUI) {
      await editorUI.loadEditorView();
    }
  }

  async loadAnalyticsView() {
    if (window.analyticsUI) {
      await analyticsUI.loadAnalyticsView();
    }
  }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.app = new CatalanApp();
  window.app.init();
});

// Handle app installation
window.addEventListener('beforeinstallprompt', (e) => {
  e.preventDefault();
  window.deferredPrompt = e;
  console.log('💾 App installation prompt available');
});

// Handle app updates
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.addEventListener('controllerchange', () => {
    console.log('🔄 App updated, reloading...');
    window.location.reload();
  });
}
