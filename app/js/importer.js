/**
 * Data Import System
 * Handles first-run import of JSONL decks with progress tracking
 */

class DataImporter {
  constructor() {
    this.isImporting = false;
    this.isPaused = false;
    this.currentStep = null;
    this.totalSteps = 0;
    this.completedSteps = 0;
    this.importManifest = null;
    this.progressCallbacks = new Map();
  }

  async checkImportStatus() {
    const status = await db.getImportStatus();
    return status.completed;
  }

  async startImport() {
    if (this.isImporting) {
      console.warn('Import already in progress');
      return;
    }

    try {
      console.log('🚀 Starting data import...');
      this.isImporting = true;
      this.isPaused = false;

      // Load import manifest
      this.importManifest = await this.loadImportManifest();
      this.totalSteps = this.importManifest.steps.length;
      this.completedSteps = 0;

      // Get existing progress
      const status = await db.getImportStatus();
      const completedSteps = status.progress || {};

      // Process each step
      for (const step of this.importManifest.steps) {
        if (this.isPaused) {
          console.log('⏸️ Import paused');
          return;
        }

        if (completedSteps[step.id]) {
          console.log(`⏭️ Skipping completed step: ${step.name}`);
          this.completedSteps++;
          this.updateProgress(step.id, 100);
          continue;
        }

        this.currentStep = step;
        await this.processStep(step);
        
        // Mark step as completed
        completedSteps[step.id] = true;
        await db.updateImportStatus({
          completed: false,
          progress: completedSteps,
          last_step: step.id,
          updated_at: new Date().toISOString()
        });

        this.completedSteps++;
      }

      // Mark import as completed
      await db.updateImportStatus({
        completed: true,
        progress: completedSteps,
        completed_at: new Date().toISOString()
      });

      this.isImporting = false;
      console.log('✅ Import completed successfully');
      this.onImportComplete();

    } catch (error) {
      console.error('❌ Import failed:', error);
      this.isImporting = false;
      this.onImportError(error);
    }
  }

  async loadImportManifest() {
    try {
      const response = await fetch('/data/import_manifest.json');
      if (!response.ok) {
        throw new Error(`Failed to load import manifest: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to load import manifest:', error);
      // Fallback to default manifest
      return this.getDefaultManifest();
    }
  }

  getDefaultManifest() {
    return {
      version: '1.0.0',
      total_steps: 5,
      estimated_total_time: 135,
      steps: [
        {
          id: 'vocabulary',
          name: 'Vocabulary Cards',
          file: 'vocabulary_decks.jsonl',
          table: 'cards',
          priority: 1,
          estimated_time: 30
        },
        {
          id: 'sentences',
          name: 'Sentence Examples',
          file: 'sentence_decks.jsonl',
          table: 'cards',
          priority: 2,
          estimated_time: 45
        },
        {
          id: 'grammar',
          name: 'Grammar Rules',
          file: 'grammar_decks.jsonl',
          table: 'cards',
          priority: 3,
          estimated_time: 20
        },
        {
          id: 'conjugations',
          name: 'Verb Conjugations',
          file: 'conjugation_decks.jsonl',
          table: 'cards',
          priority: 4,
          estimated_time: 25
        },
        {
          id: 'topics',
          name: 'Topic-Specific Content',
          file: 'topic_decks.jsonl',
          table: 'cards',
          priority: 5,
          estimated_time: 15
        }
      ]
    };
  }

  async processStep(step) {
    console.log(`📥 Processing step: ${step.name}`);
    this.updateStatus(`Importing ${step.name}...`);

    try {
      const response = await fetch(`/data/${step.file}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch ${step.file}: ${response.status}`);
      }

      const text = await response.text();
      const lines = text.trim().split('\n').filter(line => line.trim());
      
      if (lines.length === 0) {
        console.warn(`No data found in ${step.file}`);
        this.updateProgress(step.id, 100);
        return;
      }

      console.log(`📊 Processing ${lines.length} items from ${step.file}`);

      // Process in batches to avoid blocking the UI
      const batchSize = 50;
      let processed = 0;

      for (let i = 0; i < lines.length; i += batchSize) {
        if (this.isPaused) {
          console.log('⏸️ Step paused');
          return;
        }

        const batch = lines.slice(i, i + batchSize);
        const items = [];

        // Parse JSON lines
        for (const line of batch) {
          try {
            const item = JSON.parse(line);
            items.push(item);
          } catch (error) {
            console.warn(`Failed to parse line: ${line}`, error);
          }
        }

        // Import batch
        if (items.length > 0) {
          await this.importBatch(items, step.table);
        }

        processed += batch.length;
        const progress = Math.round((processed / lines.length) * 100);
        this.updateProgress(step.id, progress);

        // Allow UI to update
        await this.sleep(10);
      }

      console.log(`✅ Completed step: ${step.name} (${lines.length} items)`);

    } catch (error) {
      console.error(`❌ Failed to process step ${step.name}:`, error);
      throw error;
    }
  }

  async importBatch(items, table) {
    if (table === 'cards') {
      // Add cards to database
      for (const item of items) {
        try {
          await db.addCard(item);
        } catch (error) {
          // Ignore duplicate key errors
          if (!error.message.includes('already exists')) {
            console.warn('Failed to add card:', error);
          }
        }
      }
    }
  }

  pauseImport() {
    if (this.isImporting) {
      this.isPaused = true;
      console.log('⏸️ Import paused by user');
      this.updateStatus('Import paused...');
    }
  }

  resumeImport() {
    if (this.isImporting && this.isPaused) {
      this.isPaused = false;
      console.log('▶️ Import resumed by user');
      this.updateStatus('Resuming import...');
      
      // Continue with current step
      if (this.currentStep) {
        this.processStep(this.currentStep);
      }
    }
  }

  cancelImport() {
    this.isImporting = false;
    this.isPaused = false;
    this.currentStep = null;
    console.log('❌ Import cancelled by user');
    this.updateStatus('Import cancelled');
  }

  // Progress tracking
  onProgress(stepId, callback) {
    this.progressCallbacks.set(stepId, callback);
  }

  updateProgress(stepId, percentage) {
    const callback = this.progressCallbacks.get(stepId);
    if (callback) {
      callback(percentage);
    }

    // Update UI elements
    const progressElement = document.querySelector(`[data-progress="${stepId}"]`);
    if (progressElement) {
      progressElement.style.width = `${percentage}%`;
    }

    const textElement = document.querySelector(`[data-text="${stepId}"]`);
    if (textElement) {
      textElement.textContent = `${percentage}%`;
    }
  }

  updateStatus(message) {
    const statusElement = document.getElementById('import-status');
    if (statusElement) {
      statusElement.textContent = message;
    }
    console.log(`📋 ${message}`);
  }

  // Event handlers
  onImportComplete() {
    this.updateStatus('Import completed successfully!');
    
    // Hide import wizard
    const wizard = document.getElementById('import-wizard');
    if (wizard) {
      wizard.classList.add('hidden');
    }

    // Show main app
    const app = document.getElementById('app');
    if (app) {
      app.classList.remove('hidden');
    }

    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('importComplete'));
  }

  onImportError(error) {
    this.updateStatus(`Import failed: ${error.message}`);
    
    // Show error message
    const statusElement = document.getElementById('import-status');
    if (statusElement) {
      statusElement.innerHTML = `
        <div class="alert alert-error">
          <strong>Import Failed:</strong> ${error.message}
          <br>
          <button class="btn btn-primary btn-small mt-md" onclick="importer.startImport()">
            Retry Import
          </button>
        </div>
      `;
    }
  }

  // Utility methods
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  getEstimatedTimeRemaining() {
    if (!this.importManifest) return 0;
    
    const remainingSteps = this.importManifest.steps.slice(this.completedSteps);
    return remainingSteps.reduce((total, step) => total + step.estimated_time, 0);
  }
}

// Global importer instance
window.importer = new DataImporter();
