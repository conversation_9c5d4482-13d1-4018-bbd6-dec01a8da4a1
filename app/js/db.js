/**
 * Database layer using <PERSON>ie (IndexedDB wrapper)
 * Handles all local data storage for the Catalan Learning PWA
 */

// Import Dexie from CDN in production
// For now, we'll use a simplified implementation

class CatalanDB {
  constructor() {
    this.dbName = 'CatalanLearningPWA';
    this.version = 1;
    this.db = null;
    this.isInitialized = false;
  }

  async init() {
    if (this.isInitialized) return;

    try {
      // Open IndexedDB connection
      this.db = await this.openDatabase();
      this.isInitialized = true;
      console.log('✅ Database initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize database:', error);
      throw error;
    }
  }

  openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Cards table
        if (!db.objectStoreNames.contains('cards')) {
          const cardsStore = db.createObjectStore('cards', { keyPath: 'id' });
          cardsStore.createIndex('type', 'type', { unique: false });
          cardsStore.createIndex('cefr_level', 'cefr_level', { unique: false });
          cardsStore.createIndex('tags', 'tags', { unique: false, multiEntry: true });
          cardsStore.createIndex('source', 'source', { unique: false });
          cardsStore.createIndex('created_at', 'created_at', { unique: false });
        }

        // User progress table
        if (!db.objectStoreNames.contains('progress')) {
          const progressStore = db.createObjectStore('progress', { keyPath: 'card_id' });
          progressStore.createIndex('due_date', 'due_date', { unique: false });
          progressStore.createIndex('interval', 'interval', { unique: false });
          progressStore.createIndex('ease_factor', 'ease_factor', { unique: false });
          progressStore.createIndex('repetitions', 'repetitions', { unique: false });
          progressStore.createIndex('last_reviewed', 'last_reviewed', { unique: false });
        }

        // Study sessions table
        if (!db.objectStoreNames.contains('sessions')) {
          const sessionsStore = db.createObjectStore('sessions', { keyPath: 'id', autoIncrement: true });
          sessionsStore.createIndex('date', 'date', { unique: false });
          sessionsStore.createIndex('duration', 'duration', { unique: false });
          sessionsStore.createIndex('cards_studied', 'cards_studied', { unique: false });
        }

        // Settings table
        if (!db.objectStoreNames.contains('settings')) {
          const settingsStore = db.createObjectStore('settings', { keyPath: 'key' });
        }

        // Bookmarks table
        if (!db.objectStoreNames.contains('bookmarks')) {
          const bookmarksStore = db.createObjectStore('bookmarks', { keyPath: 'card_id' });
          bookmarksStore.createIndex('created_at', 'created_at', { unique: false });
          bookmarksStore.createIndex('tags', 'tags', { unique: false, multiEntry: true });
        }

        // Import status table
        if (!db.objectStoreNames.contains('import_status')) {
          const importStore = db.createObjectStore('import_status', { keyPath: 'id' });
        }
      };
    });
  }

  // Card operations
  async addCard(card) {
    const transaction = this.db.transaction(['cards'], 'readwrite');
    const store = transaction.objectStore('cards');
    return store.add(card);
  }

  async addCards(cards) {
    const transaction = this.db.transaction(['cards'], 'readwrite');
    const store = transaction.objectStore('cards');
    
    const promises = cards.map(card => store.add(card));
    return Promise.all(promises);
  }

  async getCard(id) {
    const transaction = this.db.transaction(['cards'], 'readonly');
    const store = transaction.objectStore('cards');
    return store.get(id);
  }

  async getCardsByType(type) {
    const transaction = this.db.transaction(['cards'], 'readonly');
    const store = transaction.objectStore('cards');
    const index = store.index('type');
    return this.getAllFromIndex(index, type);
  }

  async getCardsByCEFR(level) {
    const transaction = this.db.transaction(['cards'], 'readonly');
    const store = transaction.objectStore('cards');
    const index = store.index('cefr_level');
    return this.getAllFromIndex(index, level);
  }

  async searchCards(query) {
    const transaction = this.db.transaction(['cards'], 'readonly');
    const store = transaction.objectStore('cards');
    const cards = await this.getAllFromStore(store);
    
    const searchTerm = query.toLowerCase();
    return cards.filter(card => 
      card.catalan.toLowerCase().includes(searchTerm) ||
      (card.spanish && card.spanish.toLowerCase().includes(searchTerm)) ||
      (card.english && card.english.toLowerCase().includes(searchTerm)) ||
      (card.russian && card.russian.toLowerCase().includes(searchTerm)) ||
      (card.tags && card.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
    );
  }

  // Progress operations
  async getProgress(cardId) {
    const transaction = this.db.transaction(['progress'], 'readonly');
    const store = transaction.objectStore('progress');
    return store.get(cardId);
  }

  async updateProgress(cardId, progressData) {
    const transaction = this.db.transaction(['progress'], 'readwrite');
    const store = transaction.objectStore('progress');
    
    const progress = {
      card_id: cardId,
      ...progressData,
      last_reviewed: new Date().toISOString()
    };
    
    return store.put(progress);
  }

  async getDueCards(limit = 20) {
    try {
      const transaction = this.db.transaction(['progress'], 'readonly');
      const store = transaction.objectStore('progress');
      const allProgress = await this.getAllFromStore(store);

      const now = new Date();
      const dueCards = allProgress.filter(progress => {
        const dueDate = new Date(progress.due_date);
        return dueDate <= now;
      });

      return dueCards.slice(0, limit);
    } catch (error) {
      console.error('Error getting due cards:', error);
      return [];
    }
  }

  async getNewCards(limit = 10) {
    try {
      // Get all cards
      const cardsTransaction = this.db.transaction(['cards'], 'readonly');
      const cardsStore = cardsTransaction.objectStore('cards');
      const allCards = await this.getAllFromStore(cardsStore);

      // Get all progress records
      const progressTransaction = this.db.transaction(['progress'], 'readonly');
      const progressStore = progressTransaction.objectStore('progress');
      const allProgress = await this.getAllFromStore(progressStore);

      // Create set of studied card IDs
      const studiedCardIds = new Set(allProgress.map(p => p.card_id));

      // Filter out studied cards
      const newCards = allCards.filter(card => !studiedCardIds.has(card.id));
      return newCards.slice(0, limit);
    } catch (error) {
      console.error('Error getting new cards:', error);
      return [];
    }
  }

  // Session operations
  async addSession(sessionData) {
    const transaction = this.db.transaction(['sessions'], 'readwrite');
    const store = transaction.objectStore('sessions');
    
    const session = {
      ...sessionData,
      date: new Date().toISOString()
    };
    
    return store.add(session);
  }

  async getRecentSessions(days = 30) {
    const transaction = this.db.transaction(['sessions'], 'readonly');
    const store = transaction.objectStore('sessions');
    const index = store.index('date');
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    const range = IDBKeyRange.lowerBound(cutoffDate.toISOString());
    
    return this.getAllFromIndex(index, range);
  }

  // Bookmark operations
  async addBookmark(cardId, tags = []) {
    const transaction = this.db.transaction(['bookmarks'], 'readwrite');
    const store = transaction.objectStore('bookmarks');
    
    const bookmark = {
      card_id: cardId,
      created_at: new Date().toISOString(),
      tags
    };
    
    return store.put(bookmark);
  }

  async removeBookmark(cardId) {
    const transaction = this.db.transaction(['bookmarks'], 'readwrite');
    const store = transaction.objectStore('bookmarks');
    return store.delete(cardId);
  }

  async getBookmarks() {
    const transaction = this.db.transaction(['bookmarks'], 'readonly');
    const store = transaction.objectStore('bookmarks');
    return this.getAllFromStore(store);
  }

  async isBookmarked(cardId) {
    const transaction = this.db.transaction(['bookmarks'], 'readonly');
    const store = transaction.objectStore('bookmarks');
    const bookmark = await store.get(cardId);
    return !!bookmark;
  }

  // Settings operations
  async getSetting(key, defaultValue = null) {
    const transaction = this.db.transaction(['settings'], 'readonly');
    const store = transaction.objectStore('settings');
    const result = await store.get(key);
    return result ? result.value : defaultValue;
  }

  async setSetting(key, value) {
    const transaction = this.db.transaction(['settings'], 'readwrite');
    const store = transaction.objectStore('settings');
    return store.put({ key, value });
  }

  // Import status operations
  async getImportStatus() {
    const transaction = this.db.transaction(['import_status'], 'readonly');
    const store = transaction.objectStore('import_status');
    const result = await store.get('main');
    return result || { id: 'main', completed: false, progress: {} };
  }

  async updateImportStatus(status) {
    const transaction = this.db.transaction(['import_status'], 'readwrite');
    const store = transaction.objectStore('import_status');
    return store.put({ id: 'main', ...status });
  }

  // Statistics
  async getStatistics() {
    const [cards, progress, sessions] = await Promise.all([
      this.getAllFromStore(this.db.transaction(['cards'], 'readonly').objectStore('cards')),
      this.getAllFromStore(this.db.transaction(['progress'], 'readonly').objectStore('progress')),
      this.getAllFromStore(this.db.transaction(['sessions'], 'readonly').objectStore('sessions'))
    ]);

    const today = new Date().toDateString();
    const todaySessions = sessions.filter(s => new Date(s.date).toDateString() === today);

    return {
      total_cards: cards.length,
      studied_cards: progress.length,
      new_cards: cards.length - progress.length,
      due_cards: progress.filter(p => new Date(p.due_date) <= new Date()).length,
      today_reviews: todaySessions.reduce((sum, s) => sum + (s.cards_studied || 0), 0),
      today_time: todaySessions.reduce((sum, s) => sum + (s.duration || 0), 0),
      streak: await this.calculateStreak(sessions)
    };
  }

  async calculateStreak(sessions) {
    if (!sessions.length) return 0;
    
    const sessionDates = [...new Set(sessions.map(s => new Date(s.date).toDateString()))].sort();
    let streak = 0;
    let currentDate = new Date();
    
    for (let i = sessionDates.length - 1; i >= 0; i--) {
      const sessionDate = new Date(sessionDates[i]);
      const daysDiff = Math.floor((currentDate - sessionDate) / (1000 * 60 * 60 * 24));
      
      if (daysDiff <= 1) {
        streak++;
        currentDate = sessionDate;
      } else {
        break;
      }
    }
    
    return streak;
  }

  // Utility methods
  getAllFromStore(store) {
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  getAllFromIndex(index, query, limit) {
    return new Promise((resolve, reject) => {
      const results = [];
      const request = query ? index.openCursor(query) : index.openCursor();
      
      request.onsuccess = (event) => {
        const cursor = event.target.result;
        if (cursor && (!limit || results.length < limit)) {
          results.push(cursor.value);
          cursor.continue();
        } else {
          resolve(results);
        }
      };
      
      request.onerror = () => reject(request.error);
    });
  }

  // Cleanup and maintenance
  async clearAllData() {
    const stores = ['cards', 'progress', 'sessions', 'bookmarks'];
    const transaction = this.db.transaction(stores, 'readwrite');
    
    const promises = stores.map(storeName => {
      const store = transaction.objectStore(storeName);
      return store.clear();
    });
    
    return Promise.all(promises);
  }

  async exportData() {
    const [cards, progress, sessions, bookmarks, settings] = await Promise.all([
      this.getAllFromStore(this.db.transaction(['cards'], 'readonly').objectStore('cards')),
      this.getAllFromStore(this.db.transaction(['progress'], 'readonly').objectStore('progress')),
      this.getAllFromStore(this.db.transaction(['sessions'], 'readonly').objectStore('sessions')),
      this.getAllFromStore(this.db.transaction(['bookmarks'], 'readonly').objectStore('bookmarks')),
      this.getAllFromStore(this.db.transaction(['settings'], 'readonly').objectStore('settings'))
    ]);

    return {
      version: this.version,
      exported_at: new Date().toISOString(),
      cards,
      progress,
      sessions,
      bookmarks,
      settings
    };
  }
}

// Global database instance
window.db = new CatalanDB();
