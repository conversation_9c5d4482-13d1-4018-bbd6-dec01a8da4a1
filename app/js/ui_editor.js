/**
 * Card Editor UI
 * Handles card creation, editing, and management
 */

class EditorUI {
  constructor() {
    this.currentCard = null;
    this.isEditing = false;
  }

  async loadEditorView() {
    const editorView = document.getElementById('editor-view');
    if (!editorView) return;

    editorView.innerHTML = `
      <div class="editor-container">
        <div class="editor-header">
          <h2>📝 Editor de cartes</h2>
          <div class="editor-actions">
            <button class="btn btn-primary" onclick="editorUI.showNewCardForm()">
              ➕ Nova carta
            </button>
            <button class="btn btn-secondary" onclick="editorUI.showImportModal()">
              📥 Importar
            </button>
          </div>
        </div>
        
        <div class="editor-content">
          <div class="editor-sidebar">
            <div class="search-box">
              <input type="text" class="form-input" id="editor-search" placeholder="Cerca cartes...">
            </div>
            <div class="filter-options">
              <select class="form-input form-select" id="type-filter">
                <option value="">Tots els tipus</option>
                <option value="vocabulary">Vocabulari</option>
                <option value="sentence">Frases</option>
                <option value="grammar">Gramàtica</option>
                <option value="conjugation">Conjugacions</option>
                <option value="topic">Temes</option>
              </select>
              <select class="form-input form-select" id="cefr-filter">
                <option value="">Tots els nivells</option>
                <option value="A1">A1</option>
                <option value="A2">A2</option>
                <option value="B1">B1</option>
                <option value="B2">B2</option>
                <option value="C1">C1</option>
                <option value="C2">C2</option>
              </select>
            </div>
            <div id="cards-list" class="cards-list">
              <!-- Cards will be loaded here -->
            </div>
          </div>
          
          <div class="editor-main">
            <div id="card-form-container" class="card-form-container hidden">
              <!-- Card form will be loaded here -->
            </div>
            <div id="editor-welcome" class="editor-welcome">
              <h3>Selecciona una carta per editar</h3>
              <p>Utilitza la barra lateral per cercar i filtrar cartes, o crea una nova carta.</p>
            </div>
          </div>
        </div>
      </div>
    `;

    this.setupEditorEvents();
    await this.loadCardsList();
  }

  setupEditorEvents() {
    // Search functionality
    const searchInput = document.getElementById('editor-search');
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.filterCards();
        }, 300);
      });
    }

    // Filter functionality
    const typeFilter = document.getElementById('type-filter');
    const cefrFilter = document.getElementById('cefr-filter');
    
    if (typeFilter) {
      typeFilter.addEventListener('change', () => this.filterCards());
    }
    
    if (cefrFilter) {
      cefrFilter.addEventListener('change', () => this.filterCards());
    }
  }

  async loadCardsList() {
    const cardsList = document.getElementById('cards-list');
    if (!cardsList) return;

    try {
      cardsList.innerHTML = '<div class="loading"></div>';
      
      // Get all cards
      const allCards = await db.getAllFromStore(
        db.db.transaction(['cards'], 'readonly').objectStore('cards')
      );
      
      this.renderCardsList(allCards);
      
    } catch (error) {
      console.error('Failed to load cards:', error);
      cardsList.innerHTML = '<p class="text-error">Error al carregar les cartes</p>';
    }
  }

  renderCardsList(cards) {
    const cardsList = document.getElementById('cards-list');
    if (!cardsList) return;

    if (cards.length === 0) {
      cardsList.innerHTML = '<p class="text-secondary">No s\'han trobat cartes</p>';
      return;
    }

    cardsList.innerHTML = cards.map(card => `
      <div class="card-list-item" data-card-id="${card.id}" onclick="editorUI.selectCard('${card.id}')">
        <div class="card-list-content">
          <div class="card-list-main">
            <strong>${card.catalan}</strong>
            <span class="card-list-translation">${card.spanish || card.english || ''}</span>
          </div>
          <div class="card-list-meta">
            <span class="tag tag-outline">${card.type}</span>
            <span class="tag tag-outline">${card.cefr_level}</span>
          </div>
        </div>
        <div class="card-list-actions">
          <button class="btn btn-small btn-ghost" onclick="event.stopPropagation(); editorUI.duplicateCard('${card.id}')" title="Duplicar">
            📋
          </button>
          <button class="btn btn-small btn-ghost text-error" onclick="event.stopPropagation(); editorUI.deleteCard('${card.id}')" title="Esborrar">
            🗑️
          </button>
        </div>
      </div>
    `).join('');
  }

  async filterCards() {
    const searchQuery = document.getElementById('editor-search')?.value || '';
    const typeFilter = document.getElementById('type-filter')?.value || '';
    const cefrFilter = document.getElementById('cefr-filter')?.value || '';

    try {
      let cards = await db.getAllFromStore(
        db.db.transaction(['cards'], 'readonly').objectStore('cards')
      );

      // Apply filters
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        cards = cards.filter(card => 
          card.catalan.toLowerCase().includes(query) ||
          (card.spanish && card.spanish.toLowerCase().includes(query)) ||
          (card.english && card.english.toLowerCase().includes(query)) ||
          (card.tags && card.tags.some(tag => tag.toLowerCase().includes(query)))
        );
      }

      if (typeFilter) {
        cards = cards.filter(card => card.type === typeFilter);
      }

      if (cefrFilter) {
        cards = cards.filter(card => card.cefr_level === cefrFilter);
      }

      this.renderCardsList(cards);

    } catch (error) {
      console.error('Failed to filter cards:', error);
    }
  }

  async selectCard(cardId) {
    try {
      const card = await db.getCard(cardId);
      if (!card) {
        uiComponents.showToast('Carta no trobada', 'error');
        return;
      }

      this.currentCard = card;
      this.isEditing = true;
      this.showCardForm(card);

      // Update selection in list
      document.querySelectorAll('.card-list-item').forEach(item => {
        item.classList.remove('selected');
      });
      
      const selectedItem = document.querySelector(`[data-card-id="${cardId}"]`);
      if (selectedItem) {
        selectedItem.classList.add('selected');
      }

    } catch (error) {
      console.error('Failed to select card:', error);
      uiComponents.showToast('Error al carregar la carta', 'error');
    }
  }

  showNewCardForm() {
    this.currentCard = null;
    this.isEditing = false;
    this.showCardForm();
  }

  showCardForm(card = null) {
    const container = document.getElementById('card-form-container');
    const welcome = document.getElementById('editor-welcome');
    
    if (!container || !welcome) return;

    welcome.classList.add('hidden');
    container.classList.remove('hidden');

    const isNew = !card;
    const formTitle = isNew ? 'Nova carta' : 'Editar carta';

    container.innerHTML = `
      <div class="card-form">
        <div class="card-form-header">
          <h3>${formTitle}</h3>
          <button class="btn btn-ghost" onclick="editorUI.closeCardForm()">❌</button>
        </div>
        
        <form id="card-edit-form" onsubmit="editorUI.saveCard(event)">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">Català *</label>
              <input type="text" class="form-input" id="card-catalan" value="${card?.catalan || ''}" required>
            </div>
            <div class="form-group">
              <label class="form-label">Tipus *</label>
              <select class="form-input form-select" id="card-type" required>
                <option value="vocabulary" ${card?.type === 'vocabulary' ? 'selected' : ''}>Vocabulari</option>
                <option value="sentence" ${card?.type === 'sentence' ? 'selected' : ''}>Frase</option>
                <option value="grammar" ${card?.type === 'grammar' ? 'selected' : ''}>Gramàtica</option>
                <option value="conjugation" ${card?.type === 'conjugation' ? 'selected' : ''}>Conjugació</option>
                <option value="topic" ${card?.type === 'topic' ? 'selected' : ''}>Tema</option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">Espanyol</label>
              <input type="text" class="form-input" id="card-spanish" value="${card?.spanish || ''}">
            </div>
            <div class="form-group">
              <label class="form-label">Anglès</label>
              <input type="text" class="form-input" id="card-english" value="${card?.english || ''}">
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">Rus</label>
              <input type="text" class="form-input" id="card-russian" value="${card?.russian || ''}">
            </div>
            <div class="form-group">
              <label class="form-label">Nivell CEFR</label>
              <select class="form-input form-select" id="card-cefr">
                <option value="A1" ${card?.cefr_level === 'A1' ? 'selected' : ''}>A1</option>
                <option value="A2" ${card?.cefr_level === 'A2' ? 'selected' : ''}>A2</option>
                <option value="B1" ${card?.cefr_level === 'B1' ? 'selected' : ''}>B1</option>
                <option value="B2" ${card?.cefr_level === 'B2' ? 'selected' : ''}>B2</option>
                <option value="C1" ${card?.cefr_level === 'C1' ? 'selected' : ''}>C1</option>
                <option value="C2" ${card?.cefr_level === 'C2' ? 'selected' : ''}>C2</option>
              </select>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">IPA</label>
            <input type="text" class="form-input" id="card-ipa" value="${card?.ipa || ''}" placeholder="/ˈka.za/">
          </div>
          
          <div class="form-group">
            <label class="form-label">Etiquetes (separades per comes)</label>
            <input type="text" class="form-input" id="card-tags" value="${card?.tags?.join(', ') || ''}" placeholder="vocabulari, casa, habitatge">
          </div>
          
          <div class="form-group">
            <label class="form-label">Notes gramaticals</label>
            <textarea class="form-input form-textarea" id="card-grammar-notes">${card?.grammar_notes || ''}</textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label">Notes d'ús</label>
            <textarea class="form-input form-textarea" id="card-usage-notes">${card?.usage_notes || ''}</textarea>
          </div>
          
          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              ${isNew ? 'Crear carta' : 'Desar canvis'}
            </button>
            <button type="button" class="btn btn-secondary" onclick="editorUI.previewCard()">
              👁️ Vista prèvia
            </button>
            ${!isNew ? '<button type="button" class="btn btn-outline" onclick="editorUI.duplicateCurrentCard()">📋 Duplicar</button>' : ''}
          </div>
        </form>
      </div>
    `;
  }

  async saveCard(event) {
    event.preventDefault();
    
    const formData = {
      catalan: document.getElementById('card-catalan').value.trim(),
      type: document.getElementById('card-type').value,
      spanish: document.getElementById('card-spanish').value.trim(),
      english: document.getElementById('card-english').value.trim(),
      russian: document.getElementById('card-russian').value.trim(),
      cefr_level: document.getElementById('card-cefr').value,
      ipa: document.getElementById('card-ipa').value.trim(),
      tags: document.getElementById('card-tags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
      grammar_notes: document.getElementById('card-grammar-notes').value.trim(),
      usage_notes: document.getElementById('card-usage-notes').value.trim()
    };

    if (!formData.catalan) {
      uiComponents.showToast('El camp català és obligatori', 'error');
      return;
    }

    try {
      if (this.isEditing && this.currentCard) {
        // Update existing card
        const updatedCard = {
          ...this.currentCard,
          ...formData,
          updated_at: new Date().toISOString()
        };
        
        await db.addCard(updatedCard); // This will overwrite due to same ID
        uiComponents.showToast('Carta actualitzada', 'success');
        
      } else {
        // Create new card
        const newCard = {
          id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          ...formData,
          source: 'user_created',
          license: 'user_content',
          created_at: new Date().toISOString()
        };
        
        await db.addCard(newCard);
        uiComponents.showToast('Nova carta creada', 'success');
      }

      // Refresh cards list
      await this.loadCardsList();
      this.closeCardForm();

    } catch (error) {
      console.error('Failed to save card:', error);
      uiComponents.showToast('Error al desar la carta', 'error');
    }
  }

  async duplicateCard(cardId) {
    try {
      const card = await db.getCard(cardId);
      if (!card) return;

      const duplicatedCard = {
        ...card,
        id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        catalan: `${card.catalan} (còpia)`,
        source: 'user_duplicated',
        created_at: new Date().toISOString()
      };

      await db.addCard(duplicatedCard);
      uiComponents.showToast('Carta duplicada', 'success');
      await this.loadCardsList();

    } catch (error) {
      console.error('Failed to duplicate card:', error);
      uiComponents.showToast('Error al duplicar la carta', 'error');
    }
  }

  async duplicateCurrentCard() {
    if (this.currentCard) {
      await this.duplicateCard(this.currentCard.id);
    }
  }

  async deleteCard(cardId) {
    if (!confirm('Estàs segur que vols esborrar aquesta carta?')) {
      return;
    }

    try {
      // Note: IndexedDB doesn't have a direct delete method in our implementation
      // In a real implementation, you would add a delete method to the DB class
      uiComponents.showToast('Funcionalitat d\'esborrar no implementada encara', 'warning');

    } catch (error) {
      console.error('Failed to delete card:', error);
      uiComponents.showToast('Error al esborrar la carta', 'error');
    }
  }

  previewCard() {
    const formData = {
      catalan: document.getElementById('card-catalan').value.trim(),
      spanish: document.getElementById('card-spanish').value.trim(),
      english: document.getElementById('card-english').value.trim(),
      ipa: document.getElementById('card-ipa').value.trim()
    };

    const previewContent = `
      <div class="card-preview">
        <div class="preview-front">
          <div class="card-word">${formData.catalan}</div>
          ${formData.ipa ? `<div class="card-ipa">${formData.ipa}</div>` : ''}
        </div>
        <div class="preview-back">
          <div class="card-translation">${formData.spanish || formData.english}</div>
        </div>
      </div>
    `;

    uiComponents.createModal('card-preview', '👁️ Vista prèvia de la carta', previewContent);
    uiComponents.showModal('card-preview');
  }

  closeCardForm() {
    const container = document.getElementById('card-form-container');
    const welcome = document.getElementById('editor-welcome');
    
    if (container && welcome) {
      container.classList.add('hidden');
      welcome.classList.remove('hidden');
    }

    // Clear selection
    document.querySelectorAll('.card-list-item').forEach(item => {
      item.classList.remove('selected');
    });

    this.currentCard = null;
    this.isEditing = false;
  }

  showImportModal() {
    const content = `
      <div class="import-options">
        <div class="form-group">
          <label class="form-label">Format d'importació</label>
          <select class="form-input form-select" id="import-format">
            <option value="csv">CSV</option>
            <option value="json">JSON</option>
            <option value="tsv">TSV</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label">Fitxer</label>
          <input type="file" class="form-input" id="import-file" accept=".csv,.json,.tsv,.txt">
        </div>
        <div class="import-preview" id="import-preview" style="display: none;">
          <h4>Vista prèvia:</h4>
          <div id="import-preview-content"></div>
        </div>
      </div>
    `;

    const actions = [
      {
        text: 'Importar',
        class: 'btn-primary',
        onclick: 'editorUI.processImport()'
      }
    ];

    uiComponents.createModal('import-modal', '📥 Importar cartes', content, actions);
    uiComponents.showModal('import-modal');
  }

  async processImport() {
    const fileInput = document.getElementById('import-file');
    const format = document.getElementById('import-format').value;
    
    if (!fileInput.files[0]) {
      uiComponents.showToast('Selecciona un fitxer', 'error');
      return;
    }

    try {
      const file = fileInput.files[0];
      const text = await file.text();
      let cards = [];

      switch (format) {
        case 'csv':
          cards = this.parseCSV(text);
          break;
        case 'json':
          cards = JSON.parse(text);
          break;
        case 'tsv':
          cards = this.parseTSV(text);
          break;
      }

      if (!Array.isArray(cards) || cards.length === 0) {
        throw new Error('No s\'han trobat cartes vàlides');
      }

      // Import cards
      for (const cardData of cards) {
        const card = {
          id: `imported_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: cardData.type || 'vocabulary',
          catalan: cardData.catalan || cardData.front || '',
          spanish: cardData.spanish || cardData.back || '',
          english: cardData.english || '',
          cefr_level: cardData.cefr_level || 'A2',
          tags: Array.isArray(cardData.tags) ? cardData.tags : [],
          source: 'user_imported',
          created_at: new Date().toISOString()
        };

        if (card.catalan) {
          await db.addCard(card);
        }
      }

      uiComponents.showToast(`${cards.length} cartes importades`, 'success');
      uiComponents.closeModal('import-modal');
      await this.loadCardsList();

    } catch (error) {
      console.error('Import failed:', error);
      uiComponents.showToast('Error a l\'importar: ' + error.message, 'error');
    }
  }

  parseCSV(text) {
    const lines = text.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim());
    const cards = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      const card = {};
      
      headers.forEach((header, index) => {
        card[header.toLowerCase()] = values[index] || '';
      });
      
      cards.push(card);
    }

    return cards;
  }

  parseTSV(text) {
    const lines = text.trim().split('\n');
    const headers = lines[0].split('\t').map(h => h.trim());
    const cards = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split('\t').map(v => v.trim());
      const card = {};
      
      headers.forEach((header, index) => {
        card[header.toLowerCase()] = values[index] || '';
      });
      
      cards.push(card);
    }

    return cards;
  }
}

// Global editor UI instance
window.editorUI = new EditorUI();
