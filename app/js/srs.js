/**
 * Spaced Repetition System (SRS) Engine
 * Implements FSRS algorithm with leech detection and multiple review modes
 */

class SRSEngine {
  constructor() {
    // FSRS parameters (can be customized per user)
    this.params = {
      w: [0.4, 0.6, 2.4, 5.8, 4.93, 0.94, 0.86, 0.01, 1.49, 0.14, 0.94, 2.18, 0.05, 0.34, 1.26, 0.29, 2.61],
      requestRetention: 0.9,
      maximumInterval: 36500, // 100 years
      easyBonus: 1.3,
      hardFactor: 1.2
    };
    
    // Review modes
    this.reviewModes = {
      STANDARD: 'standard',      // CA → ES/EN/RU
      REVERSE: 'reverse',        // ES/EN/RU → CA
      LISTENING: 'listening',    // Audio → CA
      DICTATION: 'dictation',    // Audio → Type CA
      CLOZE: 'cloze',           // Fill in the blank
      SPEED: 'speed'            // Quick recognition
    };
    
    // Difficulty ratings
    this.ratings = {
      AGAIN: 1,    // Forgot completely
      HARD: 2,     // Remembered with difficulty
      GOOD: 3,     // Remembered correctly
      EASY: 4      // Remembered easily
    };
    
    // Leech detection
    this.leechThreshold = 8; // Number of lapses before marking as leech
  }

  // Main SRS calculation using FSRS algorithm
  calculateNextReview(card, rating, reviewMode = this.reviewModes.STANDARD) {
    const now = new Date();
    const progress = card.progress || this.getInitialProgress();
    
    // Apply FSRS algorithm
    const result = this.fsrs(progress, rating, now);
    
    // Adjust for review mode difficulty
    result.interval = this.adjustForReviewMode(result.interval, reviewMode);
    
    // Check for leech status
    if (rating === this.ratings.AGAIN) {
      result.lapses = (progress.lapses || 0) + 1;
      result.isLeech = result.lapses >= this.leechThreshold;
    }
    
    return result;
  }

  fsrs(progress, rating, reviewTime) {
    const { stability, difficulty, lapses = 0, state = 0 } = progress;
    
    let newStability, newDifficulty, newState;
    
    if (state === 0) { // New card
      newStability = this.initStability(rating);
      newDifficulty = this.initDifficulty(rating);
      newState = rating === this.ratings.AGAIN ? 0 : 1;
    } else if (state === 1) { // Learning
      newStability = this.nextStability(stability, rating);
      newDifficulty = this.nextDifficulty(difficulty, rating);
      newState = rating === this.ratings.AGAIN ? 0 : 2;
    } else { // Review
      newStability = this.nextStability(stability, rating);
      newDifficulty = this.nextDifficulty(difficulty, rating);
      newState = rating === this.ratings.AGAIN ? 1 : 2;
    }
    
    const interval = this.calculateInterval(newStability, this.params.requestRetention);
    const dueDate = new Date(reviewTime.getTime() + interval * 24 * 60 * 60 * 1000);
    
    return {
      stability: newStability,
      difficulty: newDifficulty,
      state: newState,
      interval: Math.min(interval, this.params.maximumInterval),
      due_date: dueDate.toISOString(),
      lapses: rating === this.ratings.AGAIN ? lapses + 1 : lapses,
      repetitions: (progress.repetitions || 0) + 1,
      ease_factor: this.calculateEaseFactor(newDifficulty),
      last_reviewed: reviewTime.toISOString()
    };
  }

  initStability(rating) {
    return this.params.w[rating - 1];
  }

  initDifficulty(rating) {
    return Math.min(Math.max(this.params.w[4] - this.params.w[5] * (rating - 3), 1), 10);
  }

  nextStability(stability, rating) {
    const hardPenalty = rating === this.ratings.HARD ? this.params.w[15] : 1;
    const easyBonus = rating === this.ratings.EASY ? this.params.easyBonus : 1;
    
    if (rating === this.ratings.AGAIN) {
      return this.params.w[11] * Math.pow(stability, this.params.w[12]) * 
             Math.pow(Math.E, this.params.w[13] * (1 - this.params.requestRetention)) *
             hardPenalty;
    } else {
      return stability * (Math.pow(Math.E, this.params.w[8]) *
             (11 - rating) * Math.pow(stability, -this.params.w[9]) *
             (Math.pow(Math.E, this.params.w[10] * (1 - this.params.requestRetention)) - 1) *
             hardPenalty + 1) * easyBonus;
    }
  }

  nextDifficulty(difficulty, rating) {
    const deltaD = -this.params.w[6] * (rating - 3);
    return Math.min(Math.max(difficulty + deltaD, 1), 10);
  }

  calculateInterval(stability, retention) {
    return Math.round(stability * Math.log(retention) / Math.log(0.9));
  }

  calculateEaseFactor(difficulty) {
    return Math.max(1.3, 2.5 - (difficulty - 1) * 0.15);
  }

  adjustForReviewMode(interval, reviewMode) {
    const adjustments = {
      [this.reviewModes.STANDARD]: 1.0,
      [this.reviewModes.REVERSE]: 0.9,    // Slightly harder
      [this.reviewModes.LISTENING]: 0.8,  // Harder
      [this.reviewModes.DICTATION]: 0.7,  // Much harder
      [this.reviewModes.CLOZE]: 0.85,     // Moderately harder
      [this.reviewModes.SPEED]: 1.1       // Slightly easier
    };
    
    return Math.round(interval * (adjustments[reviewMode] || 1.0));
  }

  getInitialProgress() {
    return {
      stability: 0,
      difficulty: 5,
      state: 0,
      interval: 0,
      lapses: 0,
      repetitions: 0,
      ease_factor: 2.5,
      due_date: new Date().toISOString(),
      last_reviewed: null
    };
  }

  // Study session management
  async generateStudySession(options = {}) {
    console.log('📊 SRS: Generating study session with options:', options);

    const {
      maxCards = 20,
      maxNewCards = 10,
      reviewMode = this.reviewModes.STANDARD,
      cefrLevels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
      cardTypes = ['vocabulary', 'sentence', 'grammar', 'conjugation', 'topic']
    } = options;

    try {
      // Get due cards (cards that need review)
      console.log('📋 SRS: Getting due cards...');
      const dueCards = await db.getDueCards(maxCards);
      console.log(`📋 SRS: Found ${dueCards.length} due cards`);

      // Get new cards if we need more
      const remainingSlots = maxCards - dueCards.length;
      const newCardsNeeded = Math.min(remainingSlots, maxNewCards);
      console.log(`📋 SRS: Need ${newCardsNeeded} new cards (remaining slots: ${remainingSlots})`);

      let newCards = [];
      if (newCardsNeeded > 0) {
        newCards = await db.getNewCards(newCardsNeeded);
        console.log(`📋 SRS: Found ${newCards.length} new cards`);
      }

      // If we still don't have enough cards, get any cards from the database
      let allCards = [...dueCards, ...newCards];
      if (allCards.length === 0) {
        console.log('📋 SRS: No due or new cards found, getting any available cards...');
        const anyCards = await db.getAllFromStore(
          db.db.transaction(['cards'], 'readonly').objectStore('cards')
        );
        allCards = anyCards.slice(0, maxCards);
        console.log(`📋 SRS: Found ${allCards.length} cards from database`);
      }
      console.log(`📋 SRS: Combined ${allCards.length} total cards`);

      // Filter by CEFR levels
      allCards = allCards.filter(card => cefrLevels.includes(card.cefr_level));
      console.log(`📋 SRS: After CEFR filter: ${allCards.length} cards`);

      // Filter by card types
      allCards = allCards.filter(card => cardTypes.includes(card.type));
      console.log(`📋 SRS: After type filter: ${allCards.length} cards`);

      // Shuffle cards
      allCards = this.shuffleArray(allCards);

      // Prepare cards for review mode
      const sessionCards = allCards.map(card => this.prepareCardForReview(card, reviewMode));
      console.log(`📋 SRS: Prepared ${sessionCards.length} cards for review`);

      const session = {
        cards: sessionCards,
        reviewMode,
        totalCards: sessionCards.length,
        newCards: newCards.length,
        dueCards: dueCards.length,
        estimatedTime: this.estimateSessionTime(sessionCards.length)
      };

      console.log('✅ SRS: Study session generated:', session);
      return session;

    } catch (error) {
      console.error('❌ SRS: Error generating study session:', error);
      throw error;
    }
  }

  prepareCardForReview(card, reviewMode) {
    const reviewCard = { ...card };
    
    switch (reviewMode) {
      case this.reviewModes.REVERSE:
        // Swap question and answer
        reviewCard.question = card.spanish || card.english || card.russian;
        reviewCard.answer = card.catalan;
        reviewCard.questionLang = 'es'; // Could be dynamic
        reviewCard.answerLang = 'ca';
        break;
        
      case this.reviewModes.LISTENING:
        reviewCard.question = card.audio_file || null;
        reviewCard.answer = card.catalan;
        reviewCard.isAudio = true;
        break;
        
      case this.reviewModes.DICTATION:
        reviewCard.question = card.audio_file || null;
        reviewCard.answer = card.catalan;
        reviewCard.isAudio = true;
        reviewCard.requiresTyping = true;
        break;
        
      case this.reviewModes.CLOZE:
        reviewCard.question = this.generateClozeQuestion(card);
        reviewCard.answer = card.catalan;
        reviewCard.isCloze = true;
        break;
        
      case this.reviewModes.SPEED:
        reviewCard.timeLimit = 5000; // 5 seconds
        reviewCard.question = card.catalan;
        reviewCard.answer = card.spanish || card.english;
        break;
        
      default: // STANDARD
        reviewCard.question = card.catalan;
        reviewCard.answer = card.spanish || card.english || card.russian;
        break;
    }
    
    return reviewCard;
  }

  generateClozeQuestion(card) {
    const sentence = card.examples && card.examples.length > 0 
      ? card.examples[0].catalan 
      : `Aquesta és una frase amb ${card.catalan}.`;
    
    // Replace the target word with a blank
    return sentence.replace(new RegExp(card.catalan, 'gi'), '_____');
  }

  // Session completion
  async completeReview(cardId, rating, reviewMode, timeSpent) {
    const card = await db.getCard(cardId);
    if (!card) {
      throw new Error('Card not found');
    }

    const progress = await db.getProgress(cardId);
    const currentProgress = progress || this.getInitialProgress();
    
    // Calculate next review
    const newProgress = this.calculateNextReview(
      { ...card, progress: currentProgress }, 
      rating, 
      reviewMode
    );
    
    // Update progress in database
    await db.updateProgress(cardId, newProgress);
    
    // Record review in session
    return {
      cardId,
      rating,
      reviewMode,
      timeSpent,
      nextDue: newProgress.due_date,
      interval: newProgress.interval,
      isLeech: newProgress.isLeech
    };
  }

  // Leech management
  async getLeechCards() {
    const allProgress = await db.getAllFromStore(
      db.db.transaction(['progress'], 'readonly').objectStore('progress')
    );
    
    return allProgress.filter(p => p.lapses >= this.leechThreshold);
  }

  async suspendLeech(cardId) {
    const progress = await db.getProgress(cardId);
    if (progress) {
      progress.suspended = true;
      progress.suspended_at = new Date().toISOString();
      await db.updateProgress(cardId, progress);
    }
  }

  async unsuspendLeech(cardId) {
    const progress = await db.getProgress(cardId);
    if (progress) {
      progress.suspended = false;
      progress.suspended_at = null;
      await db.updateProgress(cardId, progress);
    }
  }

  // Utility methods
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  estimateSessionTime(cardCount) {
    // Estimate 30 seconds per card on average
    return cardCount * 30;
  }

  // Statistics and analytics
  async getRetentionRate(days = 30) {
    const sessions = await db.getRecentSessions(days);
    if (sessions.length === 0) return 0;
    
    let totalReviews = 0;
    let correctReviews = 0;
    
    sessions.forEach(session => {
      if (session.reviews) {
        session.reviews.forEach(review => {
          totalReviews++;
          if (review.rating >= this.ratings.GOOD) {
            correctReviews++;
          }
        });
      }
    });
    
    return totalReviews > 0 ? (correctReviews / totalReviews) * 100 : 0;
  }

  async getAverageInterval() {
    const allProgress = await db.getAllFromStore(
      db.db.transaction(['progress'], 'readonly').objectStore('progress')
    );
    
    if (allProgress.length === 0) return 0;
    
    const totalInterval = allProgress.reduce((sum, p) => sum + (p.interval || 0), 0);
    return totalInterval / allProgress.length;
  }
}

// Global SRS instance
window.srs = new SRSEngine();
