/**
 * Analytics UI
 * Displays learning statistics and progress analytics
 */

class AnalyticsUI {
  constructor() {
    this.charts = new Map();
  }

  async loadAnalyticsView() {
    const analyticsView = document.getElementById('analytics-view');
    if (!analyticsView) return;

    analyticsView.innerHTML = `
      <div class="analytics-container">
        <div class="analytics-header">
          <h2>📊 Estadístiques d'aprenentatge</h2>
          <div class="analytics-controls">
            <select class="form-input form-select" id="analytics-period">
              <option value="7">Últims 7 dies</option>
              <option value="30" selected>Últims 30 dies</option>
              <option value="90">Últims 90 dies</option>
              <option value="365">Últim any</option>
            </select>
          </div>
        </div>
        
        <div class="analytics-grid">
          <div class="analytics-card">
            <h3>Resum general</h3>
            <div id="general-stats" class="stats-grid">
              <!-- General stats will be loaded here -->
            </div>
          </div>
          
          <div class="analytics-card">
            <h3>Progrés diari</h3>
            <div id="daily-progress-chart" class="chart-container">
              <!-- Daily progress chart -->
            </div>
          </div>
          
          <div class="analytics-card">
            <h3>Distribució per nivell CEFR</h3>
            <div id="cefr-distribution-chart" class="chart-container">
              <!-- CEFR distribution chart -->
            </div>
          </div>
          
          <div class="analytics-card">
            <h3>Tipus de cartes</h3>
            <div id="card-types-chart" class="chart-container">
              <!-- Card types chart -->
            </div>
          </div>
          
          <div class="analytics-card">
            <h3>Rendiment per dificultat</h3>
            <div id="difficulty-performance" class="performance-stats">
              <!-- Difficulty performance stats -->
            </div>
          </div>
          
          <div class="analytics-card">
            <h3>Cartes problemàtiques</h3>
            <div id="leech-cards" class="leech-list">
              <!-- Leech cards list -->
            </div>
          </div>
          
          <div class="analytics-card">
            <h3>Temps d'estudi</h3>
            <div id="study-time-stats" class="time-stats">
              <!-- Study time statistics -->
            </div>
          </div>
          
          <div class="analytics-card">
            <h3>Ratxa d'estudi</h3>
            <div id="study-streak" class="streak-display">
              <!-- Study streak display -->
            </div>
          </div>
        </div>
      </div>
    `;

    this.setupAnalyticsEvents();
    await this.loadAnalyticsData();
  }

  setupAnalyticsEvents() {
    const periodSelect = document.getElementById('analytics-period');
    if (periodSelect) {
      periodSelect.addEventListener('change', () => {
        this.loadAnalyticsData();
      });
    }
  }

  async loadAnalyticsData() {
    const period = parseInt(document.getElementById('analytics-period')?.value || '30');
    
    try {
      const [stats, sessions, cards, progress] = await Promise.all([
        db.getStatistics(),
        db.getRecentSessions(period),
        this.getAllCards(),
        this.getAllProgress()
      ]);

      this.renderGeneralStats(stats);
      this.renderDailyProgress(sessions);
      this.renderCEFRDistribution(cards);
      this.renderCardTypes(cards);
      this.renderDifficultyPerformance(sessions);
      this.renderLeechCards(progress);
      this.renderStudyTimeStats(sessions);
      this.renderStudyStreak(stats.streak);

    } catch (error) {
      console.error('Failed to load analytics data:', error);
    }
  }

  async getAllCards() {
    return await db.getAllFromStore(
      db.db.transaction(['cards'], 'readonly').objectStore('cards')
    );
  }

  async getAllProgress() {
    return await db.getAllFromStore(
      db.db.transaction(['progress'], 'readonly').objectStore('progress')
    );
  }

  renderGeneralStats(stats) {
    const container = document.getElementById('general-stats');
    if (!container) return;

    container.innerHTML = `
      <div class="stat-item">
        <div class="stat-value">${stats.total_cards || 0}</div>
        <div class="stat-label">Total de cartes</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">${stats.studied_cards || 0}</div>
        <div class="stat-label">Cartes estudiades</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">${stats.due_cards || 0}</div>
        <div class="stat-label">Cartes pendents</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">${stats.today_reviews || 0}</div>
        <div class="stat-label">Repassos d'avui</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">${this.formatTime(stats.today_time || 0)}</div>
        <div class="stat-label">Temps d'avui</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">${Math.round(stats.retention_rate || 0)}%</div>
        <div class="stat-label">Taxa de retenció</div>
      </div>
    `;
  }

  renderDailyProgress(sessions) {
    const container = document.getElementById('daily-progress-chart');
    if (!container) return;

    // Group sessions by date
    const dailyData = new Map();
    const today = new Date();
    
    // Initialize last 30 days with 0
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      dailyData.set(dateStr, { reviews: 0, time: 0, newCards: 0 });
    }

    // Fill with actual data
    sessions.forEach(session => {
      const dateStr = new Date(session.date).toISOString().split('T')[0];
      if (dailyData.has(dateStr)) {
        const data = dailyData.get(dateStr);
        data.reviews += session.cards_studied || 0;
        data.time += session.duration || 0;
        data.newCards += session.new_cards || 0;
      }
    });

    // Create simple bar chart
    const maxReviews = Math.max(...Array.from(dailyData.values()).map(d => d.reviews));
    const chartHTML = Array.from(dailyData.entries()).map(([date, data]) => {
      const height = maxReviews > 0 ? (data.reviews / maxReviews) * 100 : 0;
      const dateObj = new Date(date);
      const dayName = dateObj.toLocaleDateString('ca-ES', { weekday: 'short' });
      
      return `
        <div class="chart-bar" title="${date}: ${data.reviews} repassos">
          <div class="bar-fill" style="height: ${height}%"></div>
          <div class="bar-label">${dayName}</div>
        </div>
      `;
    }).join('');

    container.innerHTML = `
      <div class="simple-chart">
        ${chartHTML}
      </div>
    `;
  }

  renderCEFRDistribution(cards) {
    const container = document.getElementById('cefr-distribution-chart');
    if (!container) return;

    const distribution = cards.reduce((acc, card) => {
      acc[card.cefr_level] = (acc[card.cefr_level] || 0) + 1;
      return acc;
    }, {});

    const levels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
    const maxCount = Math.max(...Object.values(distribution));

    const chartHTML = levels.map(level => {
      const count = distribution[level] || 0;
      const percentage = maxCount > 0 ? (count / maxCount) * 100 : 0;
      
      return `
        <div class="chart-item">
          <div class="chart-label">${level}</div>
          <div class="chart-bar-horizontal">
            <div class="bar-fill" style="width: ${percentage}%"></div>
          </div>
          <div class="chart-value">${count}</div>
        </div>
      `;
    }).join('');

    container.innerHTML = chartHTML;
  }

  renderCardTypes(cards) {
    const container = document.getElementById('card-types-chart');
    if (!container) return;

    const types = cards.reduce((acc, card) => {
      acc[card.type] = (acc[card.type] || 0) + 1;
      return acc;
    }, {});

    const typeNames = {
      vocabulary: 'Vocabulari',
      sentence: 'Frases',
      grammar: 'Gramàtica',
      conjugation: 'Conjugacions',
      topic: 'Temes'
    };

    const total = cards.length;
    const chartHTML = Object.entries(types).map(([type, count]) => {
      const percentage = total > 0 ? Math.round((count / total) * 100) : 0;
      
      return `
        <div class="pie-slice" title="${typeNames[type] || type}: ${count} cartes (${percentage}%)">
          <div class="slice-label">${typeNames[type] || type}</div>
          <div class="slice-value">${count} (${percentage}%)</div>
        </div>
      `;
    }).join('');

    container.innerHTML = chartHTML;
  }

  renderDifficultyPerformance(sessions) {
    const container = document.getElementById('difficulty-performance');
    if (!container) return;

    const performance = { again: 0, hard: 0, good: 0, easy: 0 };
    let totalReviews = 0;

    sessions.forEach(session => {
      if (session.reviews) {
        session.reviews.forEach(review => {
          totalReviews++;
          switch (review.rating) {
            case 1: performance.again++; break;
            case 2: performance.hard++; break;
            case 3: performance.good++; break;
            case 4: performance.easy++; break;
          }
        });
      }
    });

    const performanceHTML = [
      { key: 'again', label: 'Altra vegada', color: 'var(--error-color)' },
      { key: 'hard', label: 'Difícil', color: 'var(--warning-color)' },
      { key: 'good', label: 'Bé', color: 'var(--success-color)' },
      { key: 'easy', label: 'Fàcil', color: 'var(--info-color)' }
    ].map(({ key, label, color }) => {
      const count = performance[key];
      const percentage = totalReviews > 0 ? Math.round((count / totalReviews) * 100) : 0;
      
      return `
        <div class="performance-item">
          <div class="performance-label">${label}</div>
          <div class="performance-bar">
            <div class="performance-fill" style="width: ${percentage}%; background: ${color}"></div>
          </div>
          <div class="performance-value">${count} (${percentage}%)</div>
        </div>
      `;
    }).join('');

    container.innerHTML = performanceHTML;
  }

  async renderLeechCards(progress) {
    const container = document.getElementById('leech-cards');
    if (!container) return;

    const leechCards = progress.filter(p => (p.lapses || 0) >= 8);
    
    if (leechCards.length === 0) {
      container.innerHTML = '<p class="text-success">🎉 No tens cartes problemàtiques!</p>';
      return;
    }

    const leechCardsHTML = await Promise.all(
      leechCards.slice(0, 10).map(async (progress) => {
        const card = await db.getCard(progress.card_id);
        if (!card) return '';
        
        return `
          <div class="leech-item">
            <div class="leech-content">
              <strong>${card.catalan}</strong>
              <span class="leech-translation">${card.spanish || card.english}</span>
            </div>
            <div class="leech-stats">
              <span class="leech-lapses">${progress.lapses} errors</span>
              <button class="btn btn-small btn-outline" onclick="analyticsUI.reviewLeechCard('${card.id}')">
                Repassar
              </button>
            </div>
          </div>
        `;
      })
    );

    container.innerHTML = leechCardsHTML.join('');
  }

  renderStudyTimeStats(sessions) {
    const container = document.getElementById('study-time-stats');
    if (!container) return;

    const totalTime = sessions.reduce((sum, session) => sum + (session.duration || 0), 0);
    const avgSessionTime = sessions.length > 0 ? Math.round(totalTime / sessions.length) : 0;
    const totalSessions = sessions.length;

    container.innerHTML = `
      <div class="time-stat">
        <div class="time-value">${this.formatTime(totalTime)}</div>
        <div class="time-label">Temps total</div>
      </div>
      <div class="time-stat">
        <div class="time-value">${this.formatTime(avgSessionTime)}</div>
        <div class="time-label">Mitjana per sessió</div>
      </div>
      <div class="time-stat">
        <div class="time-value">${totalSessions}</div>
        <div class="time-label">Sessions totals</div>
      </div>
    `;
  }

  renderStudyStreak(streak) {
    const container = document.getElementById('study-streak');
    if (!container) return;

    const streakEmoji = streak >= 30 ? '🔥' : streak >= 7 ? '⭐' : streak >= 3 ? '✨' : '📚';
    const streakMessage = streak >= 30 ? 'Increïble!' : 
                         streak >= 7 ? 'Molt bé!' : 
                         streak >= 3 ? 'Bon ritme!' : 
                         'Continua així!';

    container.innerHTML = `
      <div class="streak-display">
        <div class="streak-emoji">${streakEmoji}</div>
        <div class="streak-number">${streak}</div>
        <div class="streak-label">dies consecutius</div>
        <div class="streak-message">${streakMessage}</div>
      </div>
    `;
  }

  async reviewLeechCard(cardId) {
    // Create a single-card study session for the leech card
    const card = await db.getCard(cardId);
    if (!card) return;

    const session = {
      cards: [srs.prepareCardForReview(card, srs.reviewModes.STANDARD)],
      reviewMode: srs.reviewModes.STANDARD,
      totalCards: 1,
      newCards: 0,
      dueCards: 1,
      estimatedTime: 30
    };

    app.currentSession = session;
    app.navigateToView('study');
    studyUI.initSession(session);
  }

  formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }
}

// Global analytics UI instance
window.analyticsUI = new AnalyticsUI();
