/**
 * Reusable UI Components
 * Common interface elements and utilities
 */

class UIComponents {
  constructor() {
    this.modals = new Map();
  }

  // Modal management
  createModal(id, title, content, actions = []) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = id;
    
    const actionsHTML = actions.map(action => 
      `<button class="btn ${action.class || 'btn-primary'}" onclick="${action.onclick}">${action.text}</button>`
    ).join('');
    
    modal.innerHTML = `
      <div class="modal-content">
        <h2>${title}</h2>
        <div class="modal-body">
          ${content}
        </div>
        <div class="modal-actions">
          ${actionsHTML}
          <button class="btn btn-ghost" onclick="uiComponents.closeModal('${id}')">Cancel·lar</button>
        </div>
      </div>
    `;
    
    // Close on background click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.closeModal(id);
      }
    });
    
    document.body.appendChild(modal);
    this.modals.set(id, modal);
    
    return modal;
  }

  showModal(id) {
    const modal = this.modals.get(id);
    if (modal) {
      modal.classList.remove('hidden');
    }
  }

  closeModal(id) {
    const modal = this.modals.get(id);
    if (modal) {
      modal.classList.add('hidden');
      setTimeout(() => {
        modal.remove();
        this.modals.delete(id);
      }, 300);
    }
  }

  // Search modal
  createSearchModal() {
    const content = `
      <div class="form-group">
        <input type="text" class="form-input" id="search-input" placeholder="Cerca cartes..." autocomplete="off">
      </div>
      <div id="search-results" class="search-results">
        <p class="text-secondary">Escriu per cercar cartes...</p>
      </div>
    `;
    
    const actions = [
      {
        text: 'Estudiar seleccionades',
        class: 'btn-primary',
        onclick: 'uiComponents.studySearchResults()'
      }
    ];
    
    const modal = this.createModal('search-modal', '🔍 Cerca cartes', content, actions);
    
    // Setup search functionality
    const searchInput = modal.querySelector('#search-input');
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.performSearch(e.target.value);
        }, 300);
      });
      
      // Focus input when modal opens
      setTimeout(() => searchInput.focus(), 100);
    }
    
    return modal;
  }

  async performSearch(query) {
    const resultsContainer = document.getElementById('search-results');
    if (!resultsContainer) return;
    
    if (query.length < 2) {
      resultsContainer.innerHTML = '<p class="text-secondary">Escriu almenys 2 caràcters...</p>';
      return;
    }
    
    try {
      resultsContainer.innerHTML = '<div class="loading"></div>';
      
      const results = await db.searchCards(query);
      
      if (results.length === 0) {
        resultsContainer.innerHTML = '<p class="text-secondary">No s\'han trobat cartes.</p>';
        return;
      }
      
      resultsContainer.innerHTML = results.slice(0, 20).map(card => `
        <div class="search-result-item" data-card-id="${card.id}">
          <div class="search-result-content">
            <div class="search-result-main">
              <strong>${this.highlightText(card.catalan, query)}</strong>
              <span class="search-result-translation">${card.spanish || card.english || ''}</span>
            </div>
            <div class="search-result-meta">
              <span class="tag tag-outline">${card.type}</span>
              <span class="tag tag-outline">${card.cefr_level}</span>
            </div>
          </div>
          <input type="checkbox" class="search-result-checkbox" value="${card.id}">
        </div>
      `).join('');
      
    } catch (error) {
      console.error('Search failed:', error);
      resultsContainer.innerHTML = '<p class="text-error">Error en la cerca.</p>';
    }
  }

  highlightText(text, query) {
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  async studySearchResults() {
    const checkboxes = document.querySelectorAll('.search-result-checkbox:checked');
    const cardIds = Array.from(checkboxes).map(cb => cb.value);
    
    if (cardIds.length === 0) {
      this.showToast('Selecciona almenys una carta', 'warning');
      return;
    }
    
    // Get selected cards
    const cards = [];
    for (const cardId of cardIds) {
      const card = await db.getCard(cardId);
      if (card) cards.push(card);
    }
    
    // Create custom study session
    const session = {
      cards: cards.map(card => srs.prepareCardForReview(card, srs.reviewModes.STANDARD)),
      reviewMode: srs.reviewModes.STANDARD,
      totalCards: cards.length,
      newCards: 0,
      dueCards: cards.length,
      estimatedTime: srs.estimateSessionTime(cards.length)
    };
    
    this.closeModal('search-modal');
    app.currentSession = session;
    app.navigateToView('study');
    studyUI.initSession(session);
  }

  // Settings modal
  createSettingsModal() {
    const content = `
      <div class="settings-section">
        <h3>Configuració d'estudi</h3>
        <div class="form-group">
          <label class="form-label">Cartes noves per dia</label>
          <input type="number" class="form-input" id="setting-new-cards" value="20" min="1" max="100">
        </div>
        <div class="form-group">
          <label class="form-label">Cartes de repàs per dia</label>
          <input type="number" class="form-input" id="setting-review-cards" value="100" min="1" max="500">
        </div>
        <div class="form-group">
          <label class="form-label">Mode de repàs per defecte</label>
          <select class="form-input form-select" id="setting-review-mode">
            <option value="standard">Estàndard (CA → ES/EN)</option>
            <option value="reverse">Invers (ES/EN → CA)</option>
            <option value="mixed">Mixt</option>
          </select>
        </div>
      </div>
      
      <div class="settings-section">
        <h3>Interfície</h3>
        <div class="form-group">
          <label class="form-label">Idioma de traducció preferit</label>
          <select class="form-input form-select" id="setting-translation-lang">
            <option value="es">Espanyol</option>
            <option value="en">Anglès</option>
            <option value="ru">Rus</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label">
            <input type="checkbox" id="setting-audio-autoplay"> 
            Reproduir àudio automàticament
          </label>
        </div>
      </div>
      
      <div class="settings-section">
        <h3>Dades</h3>
        <div class="settings-actions">
          <button class="btn btn-outline" onclick="uiComponents.exportData()">
            📤 Exportar dades
          </button>
          <button class="btn btn-outline" onclick="uiComponents.importData()">
            📥 Importar dades
          </button>
          <button class="btn btn-outline text-error" onclick="uiComponents.resetData()">
            🗑️ Esborrar tot
          </button>
        </div>
      </div>
    `;
    
    const actions = [
      {
        text: 'Desar canvis',
        class: 'btn-primary',
        onclick: 'uiComponents.saveSettings()'
      }
    ];
    
    return this.createModal('settings-modal', '⚙️ Configuració', content, actions);
  }

  async saveSettings() {
    const settings = {
      newCardsPerDay: document.getElementById('setting-new-cards')?.value || 20,
      reviewCardsPerDay: document.getElementById('setting-review-cards')?.value || 100,
      defaultReviewMode: document.getElementById('setting-review-mode')?.value || 'standard',
      translationLang: document.getElementById('setting-translation-lang')?.value || 'es',
      audioAutoplay: document.getElementById('setting-audio-autoplay')?.checked || false
    };
    
    try {
      for (const [key, value] of Object.entries(settings)) {
        await db.setSetting(key, value);
      }
      
      this.showToast('Configuració desada', 'success');
      this.closeModal('settings-modal');
      
    } catch (error) {
      console.error('Failed to save settings:', error);
      this.showToast('Error al desar la configuració', 'error');
    }
  }

  // Data management
  async exportData() {
    try {
      const data = await db.exportData();
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `catalan-learning-backup-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      
      URL.revokeObjectURL(url);
      this.showToast('Dades exportades correctament', 'success');
      
    } catch (error) {
      console.error('Export failed:', error);
      this.showToast('Error a l\'exportar les dades', 'error');
    }
  }

  importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (!file) return;
      
      try {
        const text = await file.text();
        const data = JSON.parse(text);
        
        // Validate data structure
        if (!data.version || !data.cards) {
          throw new Error('Invalid backup file format');
        }
        
        // Confirm import
        if (!confirm('Això sobreescriurà totes les dades existents. Continuar?')) {
          return;
        }
        
        // Clear existing data
        await db.clearAllData();
        
        // Import new data
        if (data.cards) await db.addCards(data.cards);
        if (data.progress) {
          for (const progress of data.progress) {
            await db.updateProgress(progress.card_id, progress);
          }
        }
        if (data.settings) {
          for (const setting of data.settings) {
            await db.setSetting(setting.key, setting.value);
          }
        }
        
        this.showToast('Dades importades correctament', 'success');
        setTimeout(() => window.location.reload(), 1000);
        
      } catch (error) {
        console.error('Import failed:', error);
        this.showToast('Error a l\'importar les dades', 'error');
      }
    };
    
    input.click();
  }

  async resetData() {
    if (!confirm('Estàs segur que vols esborrar totes les dades? Aquesta acció no es pot desfer.')) {
      return;
    }
    
    if (!confirm('ÚLTIMA ADVERTÈNCIA: Això esborrarà tot el teu progrés. Continuar?')) {
      return;
    }
    
    try {
      await db.clearAllData();
      this.showToast('Totes les dades han estat esborrades', 'success');
      setTimeout(() => window.location.reload(), 1000);
      
    } catch (error) {
      console.error('Reset failed:', error);
      this.showToast('Error a l\'esborrar les dades', 'error');
    }
  }

  // Toast notifications
  showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: var(--${type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'}-color);
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      z-index: 10000;
      animation: slideInRight 0.3s ease-out;
      max-width: 300px;
      word-wrap: break-word;
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.style.animation = 'slideOutRight 0.3s ease-in';
      setTimeout(() => toast.remove(), 300);
    }, duration);
  }

  // Utility methods
  formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ca-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  }

  formatNumber(number) {
    return new Intl.NumberFormat('ca-ES').format(number);
  }
}

// Global UI components instance
window.uiComponents = new UIComponents();

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
  @keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
  
  @keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
  }
  
  .search-results {
    max-height: 400px;
    overflow-y: auto;
    margin-top: 1rem;
  }
  
  .search-result-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .search-result-item:hover {
    background: var(--surface-variant);
    border-color: var(--primary-color);
  }
  
  .search-result-content {
    flex: 1;
  }
  
  .search-result-main {
    margin-bottom: 0.25rem;
  }
  
  .search-result-translation {
    color: var(--text-secondary);
    margin-left: 0.5rem;
  }
  
  .search-result-meta {
    display: flex;
    gap: 0.25rem;
  }
  
  .settings-section {
    margin-bottom: 2rem;
  }
  
  .settings-section h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
  }
  
  .settings-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
  
  mark {
    background: var(--primary-color);
    color: white;
    padding: 0.1em 0.2em;
    border-radius: 0.2em;
  }
`;
document.head.appendChild(style);
