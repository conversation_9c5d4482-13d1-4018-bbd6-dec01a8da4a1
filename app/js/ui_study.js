/**
 * Study UI Controller
 * Handles the study session interface and card review
 */

class StudyUI {
  constructor() {
    this.currentSession = null;
    this.currentCardIndex = 0;
    this.isCardFlipped = false;
    this.sessionStartTime = null;
    this.cardStartTime = null;
    this.reviewResults = [];
  }

  initSession(session) {
    this.currentSession = session;
    this.currentCardIndex = 0;
    this.isCardFlipped = false;
    this.sessionStartTime = Date.now();
    this.reviewResults = [];
    
    console.log(`🎯 Starting study session: ${session.totalCards} cards`);
    
    this.setupStudyUI();
    this.showCurrentCard();
  }

  setupStudyUI() {
    // Update progress header
    this.updateProgressHeader();
    
    // Setup card container
    const cardContainer = document.getElementById('card-container');
    if (cardContainer) {
      cardContainer.innerHTML = `
        <div class="study-card" id="study-card">
          <div class="card-front active" id="card-front">
            <div class="card-word" id="card-question"></div>
            <div class="card-ipa" id="card-ipa"></div>
            <div class="card-hint" id="card-hint"></div>
          </div>
          <div class="card-back" id="card-back">
            <div class="card-translation" id="card-answer"></div>
            <div class="card-examples" id="card-examples"></div>
            <div class="card-notes" id="card-notes"></div>
          </div>
        </div>
      `;
    }
    
    // Setup controls
    this.setupStudyControls();
    
    // Setup keyboard shortcuts
    this.setupKeyboardShortcuts();
  }

  setupStudyControls() {
    const controlsContainer = document.getElementById('study-controls');
    if (!controlsContainer) return;
    
    if (!this.isCardFlipped) {
      // Show card button
      controlsContainer.innerHTML = `
        <button class="btn btn-primary btn-large" id="show-answer" onclick="studyUI.showAnswer()">
          Mostrar resposta
        </button>
        <button class="btn btn-ghost" id="play-audio" onclick="studyUI.playAudio()" style="display: none;">
          🔊 Reproduir
        </button>
      `;
    } else {
      // Difficulty buttons
      controlsContainer.innerHTML = `
        <button class="btn difficulty-btn difficulty-again" onclick="studyUI.rateCard(1)">
          <div>Altra vegada</div>
          <small>&lt; 1 min</small>
        </button>
        <button class="btn difficulty-btn difficulty-hard" onclick="studyUI.rateCard(2)">
          <div>Difícil</div>
          <small>&lt; 6 min</small>
        </button>
        <button class="btn difficulty-btn difficulty-good" onclick="studyUI.rateCard(3)">
          <div>Bé</div>
          <small>&lt; 10 min</small>
        </button>
        <button class="btn difficulty-btn difficulty-easy" onclick="studyUI.rateCard(4)">
          <div>Fàcil</div>
          <small>4 dies</small>
        </button>
      `;
    }
  }

  showCurrentCard() {
    if (!this.currentSession || this.currentCardIndex >= this.currentSession.cards.length) {
      this.completeSession();
      return;
    }
    
    const card = this.currentSession.cards[this.currentCardIndex];
    this.cardStartTime = Date.now();
    this.isCardFlipped = false;
    
    // Update progress
    this.updateProgressHeader();
    
    // Show card front
    this.displayCardFront(card);
    
    // Reset card state
    const studyCard = document.getElementById('study-card');
    const cardFront = document.getElementById('card-front');
    const cardBack = document.getElementById('card-back');
    
    if (studyCard && cardFront && cardBack) {
      studyCard.classList.remove('flipped');
      cardFront.classList.add('active');
      cardBack.classList.remove('active');
    }
    
    // Update controls
    this.setupStudyControls();
  }

  displayCardFront(card) {
    const questionEl = document.getElementById('card-question');
    const ipaEl = document.getElementById('card-ipa');
    const hintEl = document.getElementById('card-hint');
    
    if (questionEl) {
      questionEl.textContent = card.question || card.catalan;
    }
    
    if (ipaEl) {
      ipaEl.textContent = card.ipa || '';
      ipaEl.style.display = card.ipa ? 'block' : 'none';
    }
    
    if (hintEl) {
      hintEl.textContent = this.getCardHint(card);
      hintEl.style.display = this.getCardHint(card) ? 'block' : 'none';
    }
    
    // Show audio button if available
    const audioBtn = document.getElementById('play-audio');
    if (audioBtn && card.audio_file) {
      audioBtn.style.display = 'block';
    }
  }

  displayCardBack(card) {
    const answerEl = document.getElementById('card-answer');
    const examplesEl = document.getElementById('card-examples');
    const notesEl = document.getElementById('card-notes');
    
    if (answerEl) {
      answerEl.textContent = card.answer || card.spanish || card.english;
    }
    
    if (examplesEl && card.examples && card.examples.length > 0) {
      const exampleText = card.examples[0].catalan || '';
      examplesEl.textContent = exampleText;
      examplesEl.style.display = exampleText ? 'block' : 'none';
    }
    
    if (notesEl) {
      const notes = card.grammar_notes || card.usage_notes || '';
      notesEl.textContent = notes;
      notesEl.style.display = notes ? 'block' : 'none';
    }
  }

  getCardHint(card) {
    switch (this.currentSession.reviewMode) {
      case 'reverse':
        return `Tradueix a català`;
      case 'listening':
        return `Escolta i escriu la paraula`;
      case 'cloze':
        return `Omple el buit`;
      default:
        return card.type === 'vocabulary' ? 'Vocabulari' : 
               card.type === 'grammar' ? 'Gramàtica' : '';
    }
  }

  showAnswer() {
    if (this.isCardFlipped) return;
    
    const card = this.currentSession.cards[this.currentCardIndex];
    this.isCardFlipped = true;
    
    // Flip card animation
    const studyCard = document.getElementById('study-card');
    const cardFront = document.getElementById('card-front');
    const cardBack = document.getElementById('card-back');
    
    if (studyCard && cardFront && cardBack) {
      studyCard.classList.add('flipped');
      cardFront.classList.remove('active');
      cardBack.classList.add('active');
    }
    
    // Display back content
    this.displayCardBack(card);
    
    // Update controls
    this.setupStudyControls();
  }

  async rateCard(rating) {
    if (!this.isCardFlipped) return;
    
    const card = this.currentSession.cards[this.currentCardIndex];
    const timeSpent = Date.now() - this.cardStartTime;
    
    try {
      // Record review
      const result = await srs.completeReview(
        card.id,
        rating,
        this.currentSession.reviewMode,
        timeSpent
      );
      
      this.reviewResults.push(result);
      
      // Show feedback
      this.showRatingFeedback(rating, result);
      
      // Move to next card
      setTimeout(() => {
        this.nextCard();
      }, 1000);
      
    } catch (error) {
      console.error('Failed to record review:', error);
      this.nextCard(); // Continue anyway
    }
  }

  showRatingFeedback(rating, result) {
    const messages = {
      1: '🔄 Tornaràs a veure aquesta carta aviat',
      2: '⚠️ Carta marcada com a difícil',
      3: '✅ Bona resposta!',
      4: '🌟 Perfecte! Interval llarg'
    };
    
    const message = messages[rating] || 'Resposta registrada';
    
    // Show temporary feedback
    const feedbackEl = document.createElement('div');
    feedbackEl.className = 'rating-feedback';
    feedbackEl.textContent = message;
    feedbackEl.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--primary-color);
      color: white;
      padding: 1rem 2rem;
      border-radius: 8px;
      z-index: 1000;
      animation: fadeInOut 1s ease-in-out;
    `;
    
    document.body.appendChild(feedbackEl);
    
    setTimeout(() => {
      feedbackEl.remove();
    }, 1000);
  }

  nextCard() {
    this.currentCardIndex++;
    this.showCurrentCard();
  }

  async completeSession() {
    const sessionDuration = Date.now() - this.sessionStartTime;
    
    // Record session
    const sessionData = {
      duration: Math.round(sessionDuration / 1000),
      cards_studied: this.reviewResults.length,
      review_mode: this.currentSession.reviewMode,
      reviews: this.reviewResults,
      completed_at: new Date().toISOString()
    };
    
    try {
      await db.addSession(sessionData);
      console.log('✅ Session completed and recorded');
    } catch (error) {
      console.error('Failed to record session:', error);
    }
    
    // Show completion screen
    this.showCompletionScreen(sessionData);
  }

  showCompletionScreen(sessionData) {
    const cardContainer = document.getElementById('card-container');
    const controlsContainer = document.getElementById('study-controls');
    
    if (cardContainer) {
      const stats = this.calculateSessionStats();
      
      cardContainer.innerHTML = `
        <div class="session-complete">
          <h2>🎉 Sessió completada!</h2>
          <div class="session-stats">
            <div class="stat">
              <span class="stat-value">${stats.totalCards}</span>
              <span class="stat-label">Cartes estudiades</span>
            </div>
            <div class="stat">
              <span class="stat-value">${stats.accuracy}%</span>
              <span class="stat-label">Precisió</span>
            </div>
            <div class="stat">
              <span class="stat-value">${this.formatTime(sessionData.duration)}</span>
              <span class="stat-label">Temps total</span>
            </div>
          </div>
          <div class="session-breakdown">
            <div class="breakdown-item">
              <span class="breakdown-label">Altra vegada:</span>
              <span class="breakdown-value">${stats.again}</span>
            </div>
            <div class="breakdown-item">
              <span class="breakdown-label">Difícil:</span>
              <span class="breakdown-value">${stats.hard}</span>
            </div>
            <div class="breakdown-item">
              <span class="breakdown-label">Bé:</span>
              <span class="breakdown-value">${stats.good}</span>
            </div>
            <div class="breakdown-item">
              <span class="breakdown-label">Fàcil:</span>
              <span class="breakdown-value">${stats.easy}</span>
            </div>
          </div>
        </div>
      `;
    }
    
    if (controlsContainer) {
      controlsContainer.innerHTML = `
        <button class="btn btn-primary" onclick="app.navigateToView('dashboard')">
          Tornar a l'inici
        </button>
        <button class="btn btn-secondary" onclick="app.startStudySession()">
          Nova sessió
        </button>
      `;
    }
  }

  calculateSessionStats() {
    const stats = {
      totalCards: this.reviewResults.length,
      again: 0,
      hard: 0,
      good: 0,
      easy: 0
    };
    
    this.reviewResults.forEach(result => {
      switch (result.rating) {
        case 1: stats.again++; break;
        case 2: stats.hard++; break;
        case 3: stats.good++; break;
        case 4: stats.easy++; break;
      }
    });
    
    const correct = stats.good + stats.easy;
    stats.accuracy = stats.totalCards > 0 ? Math.round((correct / stats.totalCards) * 100) : 0;
    
    return stats;
  }

  updateProgressHeader() {
    const counterEl = document.getElementById('card-counter');
    const progressEl = document.getElementById('study-progress');
    
    if (counterEl && this.currentSession) {
      const current = this.currentCardIndex + 1;
      const total = this.currentSession.totalCards;
      counterEl.textContent = `${current} / ${total}`;
    }
    
    if (progressEl && this.currentSession) {
      const progress = ((this.currentCardIndex + 1) / this.currentSession.totalCards) * 100;
      progressEl.style.width = `${progress}%`;
    }
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      if (this.currentSession && document.getElementById('study-view').classList.contains('active')) {
        switch (e.key) {
          case ' ':
          case 'Enter':
            e.preventDefault();
            if (!this.isCardFlipped) {
              this.showAnswer();
            }
            break;
          case '1':
            if (this.isCardFlipped) this.rateCard(1);
            break;
          case '2':
            if (this.isCardFlipped) this.rateCard(2);
            break;
          case '3':
            if (this.isCardFlipped) this.rateCard(3);
            break;
          case '4':
            if (this.isCardFlipped) this.rateCard(4);
            break;
          case 'Escape':
            this.exitStudy();
            break;
        }
      }
    });
  }

  playAudio() {
    const card = this.currentSession.cards[this.currentCardIndex];
    if (card.audio_file) {
      const audio = new Audio(`/media/${card.audio_file}`);
      audio.play().catch(error => {
        console.warn('Failed to play audio:', error);
      });
    }
  }

  exitStudy() {
    if (confirm('Estàs segur que vols sortir de la sessió d\'estudi?')) {
      app.navigateToView('dashboard');
    }
  }

  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

// Global study UI instance
window.studyUI = new StudyUI();
