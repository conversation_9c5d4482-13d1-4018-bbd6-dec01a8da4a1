/**
 * Export processed data to app directory
 */

const fs = require('fs-extra');
const path = require('path');

class Exporter {
  constructor() {
    this.manifestVersion = '1.0.0';
  }

  async copyDecksToApp(outputDir, appDataDir) {
    console.log('📦 Copying decks to app...');
    
    const deckFiles = [
      'vocabulary_decks.jsonl',
      'sentence_decks.jsonl',
      'grammar_decks.jsonl',
      'conjugation_decks.jsonl',
      'topic_decks.jsonl'
    ];
    
    await fs.ensureDir(appDataDir);
    
    for (const file of deckFiles) {
      const sourcePath = path.join(outputDir, file);
      const destPath = path.join(appDataDir, file);
      
      if (await fs.pathExists(sourcePath)) {
        await fs.copy(sourcePath, destPath);
        console.log(`✅ Copied ${file}`);
      } else {
        console.warn(`⚠️ Source file not found: ${file}`);
      }
    }
  }

  async generateManifests(outputDir, appDataDir) {
    console.log('📋 Generating manifests...');
    
    await this.generateDeckManifest(appDataDir);
    await this.generateAudioManifest(appDataDir);
    await this.generateImportManifest(appDataDir);
  }

  async generateDeckManifest(appDataDir) {
    const deckFiles = [
      'vocabulary_decks.jsonl',
      'sentence_decks.jsonl', 
      'grammar_decks.jsonl',
      'conjugation_decks.jsonl',
      'topic_decks.jsonl'
    ];
    
    const decks = [];
    
    for (const file of deckFiles) {
      const filepath = path.join(appDataDir, file);
      
      if (await fs.pathExists(filepath)) {
        const stats = await fs.stat(filepath);
        const content = await fs.readFile(filepath, 'utf8');
        const lineCount = content.trim().split('\n').filter(line => line.trim()).length;
        
        const deckType = file.replace('_decks.jsonl', '');
        
        decks.push({
          id: deckType,
          name: this.getDeckDisplayName(deckType),
          description: this.getDeckDescription(deckType),
          file: file,
          card_count: lineCount,
          file_size: stats.size,
          type: deckType,
          languages: ['ca', 'es', 'en', 'ru'],
          cefr_levels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
          created_at: new Date().toISOString(),
          version: this.manifestVersion
        });
      }
    }
    
    const manifest = {
      version: this.manifestVersion,
      created_at: new Date().toISOString(),
      total_decks: decks.length,
      total_cards: decks.reduce((sum, deck) => sum + deck.card_count, 0),
      languages: ['ca', 'es', 'en', 'ru'],
      decks: decks
    };
    
    const manifestPath = path.join(appDataDir, 'deck_manifest.json');
    await fs.writeJson(manifestPath, manifest, { spaces: 2 });
    
    console.log(`✅ Generated deck manifest with ${decks.length} decks`);
  }

  async generateAudioManifest(appDataDir) {
    const audioFiles = [];
    
    // For now, create placeholder audio manifest
    // In production, this would scan actual audio files
    const sampleAudioFiles = [
      {
        id: 'casa_ca',
        word: 'casa',
        language: 'ca',
        file: 'audio/ca/casa.mp3',
        duration: 1.2,
        quality: 'high',
        speaker: 'native_female_1'
      },
      {
        id: 'menjar_ca',
        word: 'menjar',
        language: 'ca', 
        file: 'audio/ca/menjar.mp3',
        duration: 1.5,
        quality: 'high',
        speaker: 'native_male_1'
      }
    ];
    
    const manifest = {
      version: this.manifestVersion,
      created_at: new Date().toISOString(),
      total_files: sampleAudioFiles.length,
      languages: ['ca'],
      speakers: ['native_female_1', 'native_male_1'],
      audio_files: sampleAudioFiles
    };
    
    const manifestPath = path.join(appDataDir, 'audio_manifest.json');
    await fs.writeJson(manifestPath, manifest, { spaces: 2 });
    
    console.log(`✅ Generated audio manifest with ${sampleAudioFiles.length} files`);
  }

  async generateImportManifest(appDataDir) {
    const importSteps = [
      {
        id: 'vocabulary',
        name: 'Vocabulary Cards',
        file: 'vocabulary_decks.jsonl',
        table: 'cards',
        priority: 1,
        estimated_time: 30
      },
      {
        id: 'sentences',
        name: 'Sentence Examples',
        file: 'sentence_decks.jsonl',
        table: 'cards',
        priority: 2,
        estimated_time: 45
      },
      {
        id: 'grammar',
        name: 'Grammar Rules',
        file: 'grammar_decks.jsonl',
        table: 'cards',
        priority: 3,
        estimated_time: 20
      },
      {
        id: 'conjugations',
        name: 'Verb Conjugations',
        file: 'conjugation_decks.jsonl',
        table: 'cards',
        priority: 4,
        estimated_time: 25
      },
      {
        id: 'topics',
        name: 'Topic-Specific Content',
        file: 'topic_decks.jsonl',
        table: 'cards',
        priority: 5,
        estimated_time: 15
      }
    ];
    
    const manifest = {
      version: this.manifestVersion,
      created_at: new Date().toISOString(),
      total_steps: importSteps.length,
      estimated_total_time: importSteps.reduce((sum, step) => sum + step.estimated_time, 0),
      steps: importSteps
    };
    
    const manifestPath = path.join(appDataDir, 'import_manifest.json');
    await fs.writeJson(manifestPath, manifest, { spaces: 2 });
    
    console.log(`✅ Generated import manifest with ${importSteps.length} steps`);
  }

  async copyMediaToApp(outputDir, appMediaDir) {
    console.log('🎵 Copying media files...');
    
    const audioDir = path.join(appMediaDir, 'audio', 'ca');
    await fs.ensureDir(audioDir);
    
    // Create placeholder audio files
    const placeholderAudio = Buffer.from('placeholder audio data');
    const audioFiles = ['casa.mp3', 'menjar.mp3', 'aigua.mp3'];
    
    for (const file of audioFiles) {
      const audioPath = path.join(audioDir, file);
      await fs.writeFile(audioPath, placeholderAudio);
    }
    
    console.log(`✅ Created ${audioFiles.length} placeholder audio files`);
  }

  getDeckDisplayName(deckType) {
    const names = {
      'vocabulary': 'Vocabulari Essencial',
      'sentence': 'Frases d\'Exemple',
      'grammar': 'Regles Gramaticals',
      'conjugation': 'Conjugacions Verbals',
      'topic': 'Temes Especialitzats'
    };
    
    return names[deckType] || deckType;
  }

  getDeckDescription(deckType) {
    const descriptions = {
      'vocabulary': 'Paraules essencials del català amb traduccions i exemples',
      'sentence': 'Frases d\'exemple per practicar la comprensió i expressió',
      'grammar': 'Regles gramaticals fonamentals del català',
      'conjugation': 'Conjugacions dels verbs més comuns',
      'topic': 'Vocabulari especialitzat per temes específics'
    };
    
    return descriptions[deckType] || '';
  }

  async generateBuildSummary(outputDir, appDataDir) {
    const summary = {
      build_date: new Date().toISOString(),
      version: this.manifestVersion,
      files_generated: [],
      statistics: {}
    };
    
    // Collect file information
    const files = await fs.readdir(appDataDir);
    for (const file of files) {
      const filepath = path.join(appDataDir, file);
      const stats = await fs.stat(filepath);
      
      summary.files_generated.push({
        name: file,
        size: stats.size,
        modified: stats.mtime.toISOString()
      });
    }
    
    // Calculate statistics
    const deckManifest = await fs.readJson(path.join(appDataDir, 'deck_manifest.json'));
    summary.statistics = {
      total_decks: deckManifest.total_decks,
      total_cards: deckManifest.total_cards,
      total_size: summary.files_generated.reduce((sum, file) => sum + file.size, 0)
    };
    
    const summaryPath = path.join(appDataDir, 'build_summary.json');
    await fs.writeJson(summaryPath, summary, { spaces: 2 });
    
    console.log(`📊 Generated build summary`);
    return summary;
  }
}

module.exports = new Exporter();
