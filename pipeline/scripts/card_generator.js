/**
 * Learning Card Generator
 * Creates vocabulary, sentence, grammar, and topic cards with CEFR levels
 */

const fs = require('fs-extra');
const path = require('path');

class CardGenerator {
  constructor() {
    this.frequencyList = new Map();
    this.cefrLevels = {
      'A1': { minFreq: 1, maxFreq: 1000, complexity: 1 },
      'A2': { minFreq: 1001, maxFreq: 2000, complexity: 2 },
      'B1': { minFreq: 2001, maxFreq: 4000, complexity: 3 },
      'B2': { minFreq: 4001, maxFreq: 8000, complexity: 4 },
      'C1': { minFreq: 8001, maxFreq: 15000, complexity: 5 },
      'C2': { minFreq: 15001, maxFreq: Infinity, complexity: 6 }
    };
  }

  async generateVocabularyCards(outputDir, profile) {
    console.log('📝 Generating vocabulary cards...');
    
    const cards = [];
    const vocabularyWords = this.getVocabularyWords(profile);
    
    for (const word of vocabularyWords) {
      const card = await this.createVocabularyCard(word);
      cards.push(card);
    }
    
    const outputPath = path.join(outputDir, 'vocabulary_decks.jsonl');
    await this.writeCardsToFile(cards, outputPath);
    
    console.log(`✅ Generated ${cards.length} vocabulary cards`);
    return outputPath;
  }

  async createVocabularyCard(wordData) {
    const { word, frequency, pos, definition, translations } = wordData;
    
    return {
      id: `vocab_${word}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'vocabulary',
      catalan: word,
      spanish: translations.spanish || '',
      english: translations.english || '',
      russian: translations.russian || '',
      ipa: this.generateIPA(word),
      cefr_level: this.assignCEFRLevel(frequency, word),
      frequency_rank: frequency,
      tags: this.generateTags(word, pos),
      grammar_notes: this.generateGrammarNotes(word, pos),
      usage_notes: definition,
      examples: this.generateExamples(word),
      source: 'generated',
      license: 'CC-BY-SA-4.0',
      created_at: new Date().toISOString(),
      part_of_speech: pos
    };
  }

  async generateSentenceCards(outputDir, profile) {
    console.log('💬 Generating sentence cards...');
    
    const cards = [];
    const sentences = this.getSentenceData(profile);
    
    for (const sentence of sentences) {
      const card = await this.createSentenceCard(sentence);
      cards.push(card);
    }
    
    const outputPath = path.join(outputDir, 'sentence_decks.jsonl');
    await this.writeCardsToFile(cards, outputPath);
    
    console.log(`✅ Generated ${cards.length} sentence cards`);
    return outputPath;
  }

  async createSentenceCard(sentenceData) {
    const { catalan, spanish, english, russian, topic, difficulty } = sentenceData;
    
    return {
      id: `sent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'sentence',
      catalan,
      spanish: spanish || '',
      english: english || '',
      russian: russian || '',
      ipa: this.generateSentenceIPA(catalan),
      cefr_level: difficulty || this.estimateSentenceDifficulty(catalan),
      tags: [topic, 'sentence', 'conversation'],
      grammar_notes: this.analyzeSentenceGrammar(catalan),
      usage_notes: `Example sentence for ${topic} context`,
      source: 'generated',
      license: 'CC-BY-SA-4.0',
      created_at: new Date().toISOString()
    };
  }

  async generateGrammarCards(outputDir, profile) {
    console.log('📚 Generating grammar cards...');
    
    const cards = [];
    const grammarRules = this.getGrammarRules(profile);
    
    for (const rule of grammarRules) {
      const card = await this.createGrammarCard(rule);
      cards.push(card);
    }
    
    const outputPath = path.join(outputDir, 'grammar_decks.jsonl');
    await this.writeCardsToFile(cards, outputPath);
    
    console.log(`✅ Generated ${cards.length} grammar cards`);
    return outputPath;
  }

  async createGrammarCard(ruleData) {
    const { rule, explanation, examples, level } = ruleData;
    
    return {
      id: `gram_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'grammar',
      catalan: rule,
      spanish: explanation.spanish || '',
      english: explanation.english || '',
      russian: explanation.russian || '',
      cefr_level: level,
      tags: ['grammar', ruleData.category],
      grammar_notes: explanation.catalan,
      examples: examples,
      source: 'generated',
      license: 'CC-BY-SA-4.0',
      created_at: new Date().toISOString()
    };
  }

  async generateConjugationCards(outputDir, profile) {
    console.log('🔄 Generating conjugation cards...');
    
    const cards = [];
    const verbs = this.getVerbsForConjugation(profile);
    
    for (const verb of verbs) {
      const card = await this.createConjugationCard(verb);
      cards.push(card);
    }
    
    const outputPath = path.join(outputDir, 'conjugation_decks.jsonl');
    await this.writeCardsToFile(cards, outputPath);
    
    console.log(`✅ Generated ${cards.length} conjugation cards`);
    return outputPath;
  }

  async createConjugationCard(verbData) {
    const { verb, conjugations, frequency, translations } = verbData;
    
    return {
      id: `conj_${verb}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'conjugation',
      catalan: verb,
      spanish: translations.spanish || '',
      english: translations.english || '',
      russian: translations.russian || '',
      ipa: this.generateIPA(verb),
      cefr_level: this.assignCEFRLevel(frequency, verb),
      frequency_rank: frequency,
      tags: ['verb', 'conjugation'],
      conjugations: conjugations,
      examples: this.generateVerbExamples(verb, conjugations),
      source: 'generated',
      license: 'CC-BY-SA-4.0',
      created_at: new Date().toISOString()
    };
  }

  async generateTopicCards(outputDir, profile) {
    console.log('🏷️ Generating topic-specific cards...');
    
    const cards = [];
    const topics = this.getTopicData(profile);
    
    for (const topic of topics) {
      const topicCards = await this.createTopicCards(topic);
      cards.push(...topicCards);
    }
    
    const outputPath = path.join(outputDir, 'topic_decks.jsonl');
    await this.writeCardsToFile(cards, outputPath);
    
    console.log(`✅ Generated ${cards.length} topic cards`);
    return outputPath;
  }

  async createTopicCards(topicData) {
    const { topic, vocabulary, phrases, level } = topicData;
    const cards = [];
    
    // Create vocabulary cards for this topic
    for (const word of vocabulary) {
      cards.push({
        id: `topic_${topic}_${word.catalan}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'topic',
        catalan: word.catalan,
        spanish: word.spanish || '',
        english: word.english || '',
        russian: word.russian || '',
        ipa: this.generateIPA(word.catalan),
        cefr_level: level,
        tags: [topic, 'vocabulary', 'specialized'],
        usage_notes: `${topic}-related vocabulary`,
        examples: word.examples || [],
        source: 'generated',
        license: 'CC-BY-SA-4.0',
        created_at: new Date().toISOString()
      });
    }
    
    return cards;
  }

  // Data generation methods
  getVocabularyWords(profile) {
    const commonWords = [
      { word: 'casa', frequency: 100, pos: 'noun', definition: 'Edifici per viure', translations: { spanish: 'casa', english: 'house', russian: 'дом' } },
      { word: 'menjar', frequency: 150, pos: 'verb', definition: 'Ingerir aliments', translations: { spanish: 'comer', english: 'to eat', russian: 'есть' } },
      { word: 'aigua', frequency: 200, pos: 'noun', definition: 'Líquid transparent', translations: { spanish: 'agua', english: 'water', russian: 'вода' } },
      { word: 'gran', frequency: 250, pos: 'adjective', definition: 'De mida considerable', translations: { spanish: 'grande', english: 'big', russian: 'большой' } },
      { word: 'anar', frequency: 80, pos: 'verb', definition: 'Moure\'s cap a un lloc', translations: { spanish: 'ir', english: 'to go', russian: 'идти' } }
    ];
    
    const targetCount = this.getTargetCount(profile.wiktionary_entries, 2000);
    const words = [];
    
    for (let i = 0; i < targetCount; i++) {
      const baseWord = commonWords[i % commonWords.length];
      words.push({
        ...baseWord,
        frequency: baseWord.frequency + i,
        word: i > 4 ? `${baseWord.word}${i}` : baseWord.word
      });
    }
    
    return words;
  }

  getSentenceData(profile) {
    const sentences = [
      { catalan: 'Bon dia, com estàs?', spanish: 'Buenos días, ¿cómo estás?', english: 'Good morning, how are you?', russian: 'Доброе утро, как дела?', topic: 'greetings', difficulty: 'A1' },
      { catalan: 'M\'agrada la música catalana.', spanish: 'Me gusta la música catalana.', english: 'I like Catalan music.', russian: 'Мне нравится каталанская музыка.', topic: 'preferences', difficulty: 'A2' },
      { catalan: 'Avui fa molt bon temps.', spanish: 'Hoy hace muy buen tiempo.', english: 'Today the weather is very nice.', russian: 'Сегодня очень хорошая погода.', topic: 'weather', difficulty: 'A2' }
    ];
    
    const targetCount = this.getTargetCount(profile.opus_sentences, 10000);
    const result = [];
    
    for (let i = 0; i < targetCount; i++) {
      result.push(sentences[i % sentences.length]);
    }
    
    return result;
  }

  getGrammarRules(profile) {
    return [
      {
        rule: 'Articles definits: el, la, els, les',
        explanation: {
          catalan: 'Els articles definits concorden en gènere i nombre amb el substantiu',
          spanish: 'Los artículos definidos concuerdan en género y número con el sustantivo',
          english: 'Definite articles agree in gender and number with the noun',
          russian: 'Определенные артикли согласуются в роде и числе с существительным'
        },
        examples: [
          { catalan: 'el gat', spanish: 'el gato', english: 'the cat' },
          { catalan: 'la casa', spanish: 'la casa', english: 'the house' }
        ],
        level: 'A1',
        category: 'articles'
      }
    ];
  }

  getVerbsForConjugation(profile) {
    return [
      {
        verb: 'ser',
        frequency: 10,
        translations: { spanish: 'ser', english: 'to be', russian: 'быть' },
        conjugations: {
          present: ['sóc', 'ets', 'és', 'som', 'sou', 'són'],
          past: ['era', 'eres', 'era', 'érem', 'éreu', 'eren']
        }
      }
    ];
  }

  getTopicData(profile) {
    return [
      {
        topic: 'cycling',
        level: 'B1',
        vocabulary: [
          { catalan: 'bicicleta', spanish: 'bicicleta', english: 'bicycle', russian: 'велосипед' },
          { catalan: 'pedalar', spanish: 'pedalear', english: 'to pedal', russian: 'крутить педали' }
        ]
      }
    ];
  }

  // Utility methods
  assignCEFRLevel(frequency, word) {
    for (const [level, range] of Object.entries(this.cefrLevels)) {
      if (frequency >= range.minFreq && frequency <= range.maxFreq) {
        return level;
      }
    }
    return 'C2';
  }

  generateIPA(word) {
    // Simplified IPA generation - in production, use proper phonetic rules
    const ipaMap = {
      'casa': '/ˈka.za/',
      'menjar': '/mənˈʒa/',
      'aigua': '/ˈaj.ɣwa/',
      'gran': '/ˈɡɾan/',
      'anar': '/əˈna/'
    };
    
    return ipaMap[word] || `/ˈ${word.replace(/[aeiou]/g, 'ə')}/`;
  }

  generateSentenceIPA(sentence) {
    return sentence.split(' ').map(word => this.generateIPA(word)).join(' ');
  }

  generateTags(word, pos) {
    const tags = ['vocabulary', pos];
    
    // Add semantic tags based on word
    if (word.includes('casa') || word.includes('habitatge')) tags.push('housing');
    if (word.includes('menjar') || word.includes('cuina')) tags.push('food');
    
    return tags;
  }

  generateGrammarNotes(word, pos) {
    const notes = {
      'noun': 'Substantiu. Recorda concordar amb l\'article.',
      'verb': 'Verb. Conjugar segons el temps i la persona.',
      'adjective': 'Adjectiu. Concorda amb el substantiu en gènere i nombre.'
    };
    
    return notes[pos] || '';
  }

  generateExamples(word) {
    return [
      {
        catalan: `Aquesta és una ${word} molt bona.`,
        spanish: `Esta es una ${word} muy buena.`,
        english: `This is a very good ${word}.`,
        russian: `Это очень хороший ${word}.`
      }
    ];
  }

  generateVerbExamples(verb, conjugations) {
    const examples = [];
    if (conjugations.present) {
      examples.push({
        catalan: `Jo ${conjugations.present[0]} molt content.`,
        spanish: `Yo ${verb} muy contento.`,
        english: `I ${verb} very happy.`
      });
    }
    return examples;
  }

  estimateSentenceDifficulty(sentence) {
    const wordCount = sentence.split(' ').length;
    if (wordCount <= 5) return 'A1';
    if (wordCount <= 10) return 'A2';
    if (wordCount <= 15) return 'B1';
    if (wordCount <= 20) return 'B2';
    if (wordCount <= 30) return 'C1';
    return 'C2';
  }

  analyzeSentenceGrammar(sentence) {
    // Simplified grammar analysis
    if (sentence.includes('?')) return 'Frase interrogativa';
    if (sentence.includes('!')) return 'Frase exclamativa';
    return 'Frase declarativa';
  }

  getTargetCount(profileLimit, defaultMax) {
    return profileLimit === -1 ? defaultMax : Math.min(profileLimit, defaultMax);
  }

  async writeCardsToFile(cards, outputPath) {
    await fs.ensureDir(path.dirname(outputPath));
    const writeStream = fs.createWriteStream(outputPath);
    
    for (const card of cards) {
      writeStream.write(JSON.stringify(card) + '\n');
    }
    
    writeStream.end();
  }
}

module.exports = new CardGenerator();
