/**
 * Build Integrity Verification
 */

const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');

class IntegrityVerifier {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  async verifyBuild(appDataDir, appMediaDir) {
    console.log('🔍 Verifying build integrity...');
    
    this.errors = [];
    this.warnings = [];
    
    await this.verifyDirectoryStructure(appDataDir, appMediaDir);
    await this.verifyDataFiles(appDataDir);
    await this.verifyMediaFiles(appMediaDir);
    await this.verifyManifests(appDataDir);
    
    this.reportResults();
    
    if (this.errors.length > 0) {
      throw new Error(`Build verification failed with ${this.errors.length} errors`);
    }
  }

  async verifyDirectoryStructure(appDataDir, appMediaDir) {
    const requiredDirs = [
      appDataDir,
      appMediaDir,
      path.join(appMediaDir, 'audio', 'ca')
    ];
    
    for (const dir of requiredDirs) {
      if (!await fs.pathExists(dir)) {
        this.errors.push(`Missing required directory: ${dir}`);
      }
    }
  }

  async verifyDataFiles(appDataDir) {
    const requiredFiles = [
      'vocabulary_decks.jsonl',
      'sentence_decks.jsonl',
      'grammar_decks.jsonl',
      'conjugation_decks.jsonl',
      'topic_decks.jsonl',
      'deck_manifest.json',
      'audio_manifest.json'
    ];
    
    for (const file of requiredFiles) {
      const filepath = path.join(appDataDir, file);
      
      if (!await fs.pathExists(filepath)) {
        this.errors.push(`Missing required data file: ${file}`);
        continue;
      }
      
      const stats = await fs.stat(filepath);
      if (stats.size === 0) {
        this.errors.push(`Empty data file: ${file}`);
        continue;
      }
      
      // Verify JSONL format
      if (file.endsWith('.jsonl')) {
        await this.verifyJsonlFile(filepath, file);
      }
      
      // Verify JSON format
      if (file.endsWith('.json')) {
        await this.verifyJsonFile(filepath, file);
      }
    }
  }

  async verifyJsonlFile(filepath, filename) {
    try {
      const content = await fs.readFile(filepath, 'utf8');
      const lines = content.trim().split('\n');
      
      if (lines.length === 0) {
        this.errors.push(`Empty JSONL file: ${filename}`);
        return;
      }
      
      let validLines = 0;
      for (let i = 0; i < Math.min(lines.length, 10); i++) {
        try {
          const parsed = JSON.parse(lines[i]);
          if (parsed && typeof parsed === 'object') {
            validLines++;
          }
        } catch (error) {
          this.errors.push(`Invalid JSON in ${filename} line ${i + 1}: ${error.message}`);
        }
      }
      
      if (validLines === 0) {
        this.errors.push(`No valid JSON objects found in ${filename}`);
      }
      
      console.log(`✅ ${filename}: ${lines.length} entries`);
      
    } catch (error) {
      this.errors.push(`Failed to read ${filename}: ${error.message}`);
    }
  }

  async verifyJsonFile(filepath, filename) {
    try {
      const content = await fs.readFile(filepath, 'utf8');
      const parsed = JSON.parse(content);
      
      if (!parsed || typeof parsed !== 'object') {
        this.errors.push(`Invalid JSON structure in ${filename}`);
        return;
      }
      
      console.log(`✅ ${filename}: Valid JSON`);
      
    } catch (error) {
      this.errors.push(`Invalid JSON in ${filename}: ${error.message}`);
    }
  }

  async verifyMediaFiles(appMediaDir) {
    const audioDir = path.join(appMediaDir, 'audio', 'ca');
    
    if (!await fs.pathExists(audioDir)) {
      this.warnings.push('Audio directory not found - audio features will be limited');
      return;
    }
    
    const audioFiles = await fs.readdir(audioDir);
    console.log(`📁 Found ${audioFiles.length} audio files`);
    
    if (audioFiles.length === 0) {
      this.warnings.push('No audio files found - audio features will be limited');
    }
  }

  async verifyManifests(appDataDir) {
    // Verify deck manifest
    const deckManifestPath = path.join(appDataDir, 'deck_manifest.json');
    if (await fs.pathExists(deckManifestPath)) {
      const manifest = await fs.readJson(deckManifestPath);
      
      if (!manifest.decks || !Array.isArray(manifest.decks)) {
        this.errors.push('Invalid deck manifest structure');
      } else {
        console.log(`📋 Deck manifest: ${manifest.decks.length} decks`);
      }
    }
    
    // Verify audio manifest
    const audioManifestPath = path.join(appDataDir, 'audio_manifest.json');
    if (await fs.pathExists(audioManifestPath)) {
      const manifest = await fs.readJson(audioManifestPath);
      
      if (!manifest.audio_files || !Array.isArray(manifest.audio_files)) {
        this.errors.push('Invalid audio manifest structure');
      } else {
        console.log(`🔊 Audio manifest: ${manifest.audio_files.length} files`);
      }
    }
  }

  reportResults() {
    console.log('\n📊 Build Verification Results:');
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('✅ All checks passed!');
      return;
    }
    
    if (this.errors.length > 0) {
      console.log(`\n❌ Errors (${this.errors.length}):`);
      this.errors.forEach(error => console.log(`  • ${error}`));
    }
    
    if (this.warnings.length > 0) {
      console.log(`\n⚠️ Warnings (${this.warnings.length}):`);
      this.warnings.forEach(warning => console.log(`  • ${warning}`));
    }
  }

  async generateBuildReport(appDataDir) {
    const report = {
      timestamp: new Date().toISOString(),
      errors: this.errors,
      warnings: this.warnings,
      statistics: await this.gatherStatistics(appDataDir)
    };
    
    const reportPath = path.join(appDataDir, 'build_report.json');
    await fs.writeJson(reportPath, report, { spaces: 2 });
    
    console.log(`📄 Build report saved to: ${reportPath}`);
    return report;
  }

  async gatherStatistics(appDataDir) {
    const stats = {
      total_cards: 0,
      decks_by_type: {},
      cefr_distribution: {},
      file_sizes: {}
    };
    
    try {
      const files = await fs.readdir(appDataDir);
      
      for (const file of files) {
        if (file.endsWith('.jsonl')) {
          const filepath = path.join(appDataDir, file);
          const content = await fs.readFile(filepath, 'utf8');
          const lines = content.trim().split('\n').filter(line => line.trim());
          
          stats.total_cards += lines.length;
          stats.decks_by_type[file] = lines.length;
          
          const fileStats = await fs.stat(filepath);
          stats.file_sizes[file] = fileStats.size;
        }
      }
    } catch (error) {
      console.warn('Failed to gather statistics:', error.message);
    }
    
    return stats;
  }
}

module.exports = new IntegrityVerifier();
