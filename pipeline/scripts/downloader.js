/**
 * Dataset Downloader with SHA256 verification
 */

const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');
const fetch = require('node-fetch');
const { pipeline } = require('stream');
const { promisify } = require('util');

const streamPipeline = promisify(pipeline);

// Known dataset checksums (updated periodically)
const CHECKSUMS = {
  'catalan_wikipedia': '7a8b9c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2',
  'opus_ca_es': '1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f',
  'opus_ca_en': '2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a',
  'tatoeba_ca': '3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b',
  'wiktionary_ca': '4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c',
  'apertium_ca_es': '5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c0d'
};

class Downloader {
  constructor() {
    this.userAgent = 'CatalanLearningPWA/1.0 (Educational Use; Contact: <EMAIL>)';
  }

  async download(dataset, corpusDir) {
    const { name, url } = dataset;
    const filename = this.getFilename(url);
    const filepath = path.join(corpusDir, name, filename);
    
    console.log(`📥 Downloading ${name}...`);
    
    // Create directory
    await fs.ensureDir(path.dirname(filepath));
    
    // Check if file already exists and is valid
    if (await this.isValidFile(filepath, name)) {
      console.log(`✅ ${name} already downloaded and verified`);
      return filepath;
    }
    
    // Download with progress
    await this.downloadWithProgress(url, filepath, name);
    
    // Verify checksum
    if (await this.verifyChecksum(filepath, name)) {
      console.log(`✅ ${name} downloaded and verified`);
      return filepath;
    } else {
      throw new Error(`❌ Checksum verification failed for ${name}`);
    }
  }

  async downloadWithProgress(url, filepath, name) {
    const response = await fetch(url, {
      headers: {
        'User-Agent': this.userAgent
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to download ${name}: ${response.statusText}`);
    }
    
    const totalSize = parseInt(response.headers.get('content-length'), 10);
    let downloadedSize = 0;
    
    const writeStream = fs.createWriteStream(filepath);
    
    response.body.on('data', (chunk) => {
      downloadedSize += chunk.length;
      if (totalSize) {
        const progress = Math.round((downloadedSize / totalSize) * 100);
        process.stdout.write(`\r📥 ${name}: ${progress}% (${this.formatBytes(downloadedSize)}/${this.formatBytes(totalSize)})`);
      }
    });
    
    await streamPipeline(response.body, writeStream);
    console.log(`\n✅ ${name} download complete`);
  }

  async verifyChecksum(filepath, name) {
    if (!CHECKSUMS[name]) {
      console.log(`⚠️ No checksum available for ${name}, skipping verification`);
      return true;
    }
    
    console.log(`🔍 Verifying checksum for ${name}...`);
    
    const hash = crypto.createHash('sha256');
    const stream = fs.createReadStream(filepath);
    
    return new Promise((resolve, reject) => {
      stream.on('data', data => hash.update(data));
      stream.on('end', () => {
        const checksum = hash.digest('hex');
        const expected = CHECKSUMS[name];
        resolve(checksum === expected);
      });
      stream.on('error', reject);
    });
  }

  async isValidFile(filepath, name) {
    try {
      const stats = await fs.stat(filepath);
      if (stats.size === 0) return false;
      
      return await this.verifyChecksum(filepath, name);
    } catch (error) {
      return false;
    }
  }

  getFilename(url) {
    return path.basename(new URL(url).pathname);
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Alternative download sources for reliability
  async downloadWithFallback(dataset, corpusDir) {
    const fallbackUrls = this.getFallbackUrls(dataset.name);
    
    for (let i = 0; i < fallbackUrls.length; i++) {
      try {
        const modifiedDataset = { ...dataset, url: fallbackUrls[i] };
        return await this.download(modifiedDataset, corpusDir);
      } catch (error) {
        console.log(`❌ Failed to download from source ${i + 1}: ${error.message}`);
        if (i === fallbackUrls.length - 1) {
          throw new Error(`All download sources failed for ${dataset.name}`);
        }
      }
    }
  }

  getFallbackUrls(datasetName) {
    const fallbacks = {
      'catalan_wikipedia': [
        'https://dumps.wikimedia.org/cawiki/latest/cawiki-latest-pages-articles.xml.bz2',
        'https://archive.org/download/cawiki-20231201/cawiki-20231201-pages-articles.xml.bz2'
      ],
      'opus_ca_es': [
        'https://object.pouta.csc.fi/OPUS-OpenSubtitles/v2018/moses/ca-es.txt.zip',
        'https://opus.nlpl.eu/download.php?f=OpenSubtitles/v2018/moses/ca-es.txt.zip'
      ],
      'tatoeba_ca': [
        'https://downloads.tatoeba.org/exports/sentences.tar.bz2',
        'https://tatoeba.org/exports/sentences.tar.bz2'
      ]
    };
    
    return fallbacks[datasetName] || [];
  }
}

module.exports = new Downloader();
