#!/usr/bin/env node

/**
 * Catalan Learning PWA - Main Build Pipeline
 * Downloads, processes, and generates complete offline learning content
 */

const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');

// Import pipeline modules
const downloader = require('./scripts/downloader');
const processor = require('./scripts/processor');
const cardGenerator = require('./scripts/card_generator');
const exporter = require('./scripts/exporter');
const verifier = require('./scripts/verify_integrity');

const PROFILES = {
  lite: {
    wikipedia_articles: 1000,
    opus_sentences: 10000,
    tatoeba_sentences: 5000,
    wiktionary_entries: 2000,
    audio_files: 500
  },
  standard: {
    wikipedia_articles: 10000,
    opus_sentences: 100000,
    tatoeba_sentences: 25000,
    wiktionary_entries: 10000,
    audio_files: 2500
  },
  full: {
    wikipedia_articles: -1, // All available
    opus_sentences: -1,
    tatoeba_sentences: -1,
    wiktionary_entries: -1,
    audio_files: -1
  }
};

class BuildPipeline {
  constructor(profile = 'standard') {
    this.profile = PROFILES[profile] || PROFILES.standard;
    this.profileName = profile;
    this.corpusDir = path.join(__dirname, 'corpus');
    this.outputDir = path.join(__dirname, 'output');
    this.appDataDir = path.join(__dirname, '..', 'app', 'data');
    this.appMediaDir = path.join(__dirname, '..', 'app', 'media');
    
    console.log(`🚀 Starting Catalan Learning PWA build (${profile} profile)`);
  }

  async initialize() {
    console.log('📁 Initializing directories...');
    await fs.ensureDir(this.corpusDir);
    await fs.ensureDir(this.outputDir);
    await fs.ensureDir(this.appDataDir);
    await fs.ensureDir(this.appMediaDir);
    await fs.ensureDir(path.join(this.appMediaDir, 'audio', 'ca'));
  }

  async downloadDatasets() {
    console.log('📥 Downloading datasets...');
    
    const datasets = [
      { name: 'catalan_wikipedia', url: 'https://dumps.wikimedia.org/cawiki/latest/cawiki-latest-pages-articles.xml.bz2' },
      { name: 'opus_ca_es', url: 'https://object.pouta.csc.fi/OPUS-OpenSubtitles/v2018/moses/ca-es.txt.zip' },
      { name: 'opus_ca_en', url: 'https://object.pouta.csc.fi/OPUS-OpenSubtitles/v2018/moses/ca-en.txt.zip' },
      { name: 'tatoeba_ca', url: 'https://downloads.tatoeba.org/exports/sentences.tar.bz2' },
      { name: 'wiktionary_ca', url: 'https://dumps.wikimedia.org/cawiki/latest/cawiktionary-latest-pages-articles.xml.bz2' },
      { name: 'apertium_ca_es', url: 'https://github.com/apertium/apertium-cat-spa/archive/refs/heads/master.zip' }
    ];

    for (const dataset of datasets) {
      await downloader.download(dataset, this.corpusDir);
    }
  }

  async processData() {
    console.log('⚙️ Processing datasets...');
    
    // Extract and normalize text
    await processor.extractWikipedia(
      path.join(this.corpusDir, 'catalan_wikipedia'),
      this.profile.wikipedia_articles
    );
    
    await processor.extractOpus(
      path.join(this.corpusDir, 'opus_ca_es'),
      path.join(this.corpusDir, 'opus_ca_en'),
      this.profile.opus_sentences
    );
    
    await processor.extractTatoeba(
      path.join(this.corpusDir, 'tatoeba_ca'),
      this.profile.tatoeba_sentences
    );
    
    await processor.extractWiktionary(
      path.join(this.corpusDir, 'wiktionary_ca'),
      this.profile.wiktionary_entries
    );
  }

  async generateCards() {
    console.log('🃏 Generating learning cards...');
    
    await cardGenerator.generateVocabularyCards(this.outputDir, this.profile);
    await cardGenerator.generateSentenceCards(this.outputDir, this.profile);
    await cardGenerator.generateGrammarCards(this.outputDir, this.profile);
    await cardGenerator.generateConjugationCards(this.outputDir, this.profile);
    await cardGenerator.generateTopicCards(this.outputDir, this.profile);
  }

  async exportToApp() {
    console.log('📦 Exporting to app...');
    
    await exporter.copyDecksToApp(this.outputDir, this.appDataDir);
    await exporter.generateManifests(this.outputDir, this.appDataDir);
    await exporter.copyMediaToApp(this.outputDir, this.appMediaDir);
  }

  async verify() {
    console.log('✅ Verifying build integrity...');
    await verifier.verifyBuild(this.appDataDir, this.appMediaDir);
  }

  async run() {
    try {
      const startTime = Date.now();
      
      await this.initialize();
      await this.downloadDatasets();
      await this.processData();
      await this.generateCards();
      await this.exportToApp();
      await this.verify();
      
      const duration = Math.round((Date.now() - startTime) / 1000);
      console.log(`✨ Build completed successfully in ${duration}s`);
      console.log(`📊 Profile: ${this.profileName}`);
      console.log(`📁 App ready at: ${path.join(__dirname, '..', 'app')}`);
      console.log(`🚀 Run: cd app && python3 -m http.server 8080`);
      
    } catch (error) {
      console.error('❌ Build failed:', error.message);
      process.exit(1);
    }
  }
}

// CLI handling
const args = process.argv.slice(2);
const profileArg = args.find(arg => arg.startsWith('--profile='));
const profile = profileArg ? profileArg.split('=')[1] : 'standard';

if (!PROFILES[profile]) {
  console.error(`❌ Invalid profile: ${profile}. Available: ${Object.keys(PROFILES).join(', ')}`);
  process.exit(1);
}

// Run the pipeline
const pipeline = new BuildPipeline(profile);
pipeline.run().catch(console.error);
