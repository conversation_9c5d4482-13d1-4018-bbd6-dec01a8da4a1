{"name": "catalan-learning-pwa", "version": "1.0.0", "description": "Complete offline Catalan learning PWA with automated data pipeline", "main": "pipeline/build_full_profile.js", "scripts": {"build": "node pipeline/build_full_profile.js", "build:lite": "node pipeline/build_full_profile.js --profile=lite", "build:standard": "node pipeline/build_full_profile.js --profile=standard", "build:full": "node pipeline/build_full_profile.js --profile=full", "serve": "cd app && python3 -m http.server 8080", "serve:node": "cd app && npx http-server -p 8080 -c-1", "clean": "rm -rf pipeline/corpus/* pipeline/output/* app/data/*", "verify": "node pipeline/scripts/verify_integrity.js"}, "keywords": ["catalan", "language-learning", "pwa", "offline", "srs"], "author": "Catalan Learning PWA", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "crypto": "^1.0.1", "fs-extra": "^11.1.1", "jsdom": "^23.0.1", "node-fetch": "^3.3.2", "xml2js": "^0.6.2", "yauzl": "^2.10.0", "zlib": "^1.0.5"}, "devDependencies": {"http-server": "^14.1.1"}, "engines": {"node": ">=16.0.0"}}