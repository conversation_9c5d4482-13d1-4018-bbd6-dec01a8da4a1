# Aprèn <PERSON> - Complete Offline Catalan Learning PWA

A comprehensive, fully offline Progressive Web App for learning Catalan with automated data pipeline, spaced repetition system, and multiple review modes.

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- Python 3.7+ (for local server)
- 2GB+ free disk space
- Modern web browser with IndexedDB support

### One-Command Setup

```bash
# Clone and setup
git clone <repository-url>
cd catalan-learning-pwa

# Install dependencies
npm install

# Build complete dataset (this may take 15-30 minutes)
npm run build:full

# Launch the app
npm run serve
```

Then open http://localhost:8080 in your browser.

## 📁 Project Structure

```
/catalan-learning-pwa
├── pipeline/                 # Data processing pipeline
│   ├── build_full_profile.js # Main build script
│   ├── scripts/              # Processing modules
│   │   ├── downloader.js     # Dataset downloader with verification
│   │   ├── processor.js      # Text processing and normalization
│   │   ├── card_generator.js # Learning card generation
│   │   ├── exporter.js       # Export to app format
│   │   └── verify_integrity.js # Build verification
│   ├── schemas/              # Data schemas
│   ├── corpus/               # Downloaded raw datasets (auto-created)
│   └── output/               # Processed data (auto-created)
├── app/                      # PWA application
│   ├── index.html            # Main app entry point
│   ├── manifest.webmanifest  # PWA manifest
│   ├── sw.js                 # Service worker for offline functionality
│   ├── css/                  # Stylesheets
│   │   ├── main.css          # Core styles
│   │   ├── components.css    # UI components
│   │   └── responsive.css    # Responsive design
│   ├── js/                   # JavaScript modules
│   │   ├── db.js             # IndexedDB database layer
│   │   ├── importer.js       # First-run data import
│   │   ├── srs.js            # Spaced repetition system (FSRS)
│   │   ├── main.js           # Main app controller
│   │   ├── ui_study.js       # Study session interface
│   │   ├── ui_editor.js      # Card editor interface
│   │   ├── ui_analytics.js   # Analytics and statistics
│   │   └── ui_components.js  # Reusable UI components
│   ├── data/                 # Learning content (auto-generated)
│   └── media/                # Audio and media files (auto-generated)
└── package.json              # Dependencies and scripts
```

## 🔧 Build Profiles

Choose your build profile based on your needs:

### Lite Profile (Quick Start)
```bash
npm run build:lite
```
- 1,000 Wikipedia articles
- 10,000 OPUS sentences  
- 5,000 Tatoeba sentences
- 2,000 Wiktionary entries
- 500 audio files
- **Build time:** ~5 minutes
- **Storage:** ~50MB

### Standard Profile (Recommended)
```bash
npm run build:standard
# or simply: npm run build
```
- 10,000 Wikipedia articles
- 100,000 OPUS sentences
- 25,000 Tatoeba sentences  
- 10,000 Wiktionary entries
- 2,500 audio files
- **Build time:** ~15 minutes
- **Storage:** ~200MB

### Full Profile (Complete Dataset)
```bash
npm run build:full
```
- All available Wikipedia articles
- All available OPUS sentences
- All available Tatoeba sentences
- All available Wiktionary entries
- All available audio files
- **Build time:** ~30-60 minutes
- **Storage:** ~500MB-1GB

## 📚 Features

### Learning System
- **Spaced Repetition:** Advanced FSRS algorithm with leech detection
- **Multiple Review Modes:**
  - Standard (CA → ES/EN/RU)
  - Reverse (ES/EN/RU → CA)
  - Listening (Audio → CA)
  - Dictation (Audio → Type CA)
  - Cloze (Fill in the blank)
  - Speed Round (Quick recognition)

### Content
- **Multi-CEFR Levels:** A1 through C2 difficulty classification
- **Comprehensive Coverage:**
  - Essential vocabulary with frequency rankings
  - Grammar rules and explanations
  - Verb conjugations
  - Example sentences with context
  - Topic-specific content (cycling, mountains, weather, etc.)
- **Multilingual:** Catalan with Spanish, English, and Russian translations
- **IPA Pronunciation:** Phonetic transcriptions for proper pronunciation

### Interface
- **Fully Offline:** Works without internet after initial setup
- **Progressive Web App:** Installable on desktop and mobile
- **Responsive Design:** Optimized for all screen sizes
- **Card Editor:** Create, edit, and import custom cards
- **Analytics:** Detailed learning statistics and progress tracking
- **Search & Filter:** Find cards by content, tags, or difficulty
- **Bookmarks:** Save important cards for quick access

## 🛠 Development

### Running in Development
```bash
# Start development server
npm run serve

# Alternative with Node.js
npm run serve:node

# Clean build artifacts
npm run clean

# Verify build integrity
npm run verify
```

### Data Pipeline Details

The build process automatically:

1. **Downloads** datasets from official sources:
   - Catalan Wikipedia dumps
   - OPUS parallel corpora (CA-ES, CA-EN)
   - Tatoeba sentence collections
   - Wiktionary entries
   - Apertium linguistic data

2. **Verifies** file integrity with SHA256 checksums

3. **Processes** raw data:
   - Extracts and normalizes text
   - Aligns parallel sentences
   - Assigns CEFR difficulty levels
   - Generates IPA pronunciations
   - Detects multi-word expressions

4. **Generates** learning cards:
   - Vocabulary cards with translations
   - Sentence examples with context
   - Grammar explanations
   - Verb conjugation tables
   - Topic-specific collections

5. **Exports** to app-ready format:
   - JSONL files for efficient streaming
   - Manifests for import tracking
   - Audio file references

### Expanding Content

To add more datasets or languages:

1. **Add new downloaders** in `pipeline/scripts/downloader.js`
2. **Extend processors** in `pipeline/scripts/processor.js`  
3. **Update card generators** in `pipeline/scripts/card_generator.js`
4. **Modify schemas** in `pipeline/schemas/`

### Custom Audio

To add audio files:

1. Place audio files in `app/media/audio/ca/`
2. Update `audio_manifest.json` with file references
3. Ensure cards reference correct audio file paths

## 📱 Usage

### First Launch
1. Open the app in your browser
2. Wait for the automatic import process (shows progress)
3. Import can be paused/resumed if needed
4. Once complete, all features are available offline

### Study Sessions
1. Click "Començar a estudiar" on the dashboard
2. Review cards using the difficulty buttons:
   - **Altra vegada** (Again): Forgot completely
   - **Difícil** (Hard): Remembered with difficulty  
   - **Bé** (Good): Remembered correctly
   - **Fàcil** (Easy): Remembered easily
3. Use keyboard shortcuts: Space/Enter to reveal, 1-4 for difficulty

### Card Management
1. Go to the Editor tab
2. Search and filter existing cards
3. Create new cards or edit existing ones
4. Import cards from CSV/JSON/TSV files
5. Export your custom cards

### Analytics
1. View detailed learning statistics
2. Track daily progress and streaks
3. Identify problematic cards (leeches)
4. Monitor retention rates and study time

## 🔒 Data & Privacy

- **Fully Local:** All data stored in browser's IndexedDB
- **No Tracking:** No analytics or telemetry sent anywhere
- **Offline First:** No network requests after initial setup
- **Export/Import:** Full control over your learning data
- **Open Source:** All code and data sources are transparent

## 📄 Licenses & Attribution

### Content Sources
- **Wikipedia:** CC-BY-SA 3.0
- **OPUS Corpora:** Various open licenses
- **Tatoeba:** CC-BY 2.0  
- **Wiktionary:** CC-BY-SA 3.0
- **Apertium:** GPL v3

### Code
- **Application:** MIT License
- **Dependencies:** See package.json for individual licenses

## 🐛 Troubleshooting

### Build Issues
```bash
# Clear cache and rebuild
npm run clean
npm install
npm run build
```

### Import Fails
- Check browser storage quota (may need 500MB+)
- Try Lite profile first: `npm run build:lite`
- Clear browser data and retry

### Performance Issues
- Close other browser tabs
- Use Chrome/Firefox for best performance
- Consider Lite profile on older devices

### Audio Not Working
- Check browser audio permissions
- Verify audio files in `app/media/audio/ca/`
- Some browsers require user interaction before audio

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your improvements
4. Test with `npm run build && npm run verify`
5. Submit a pull request

### Areas for Contribution
- Additional language pairs
- More audio recordings
- Enhanced IPA generation
- New review modes
- UI/UX improvements
- Performance optimizations

## 📞 Support

For issues, questions, or contributions:
- Open an issue on GitHub
- Check existing documentation
- Review troubleshooting section

---

**Aprèn Català** - Learn Catalan the modern way! 🏴󠁥󠁳󠁣󠁴󠁿
